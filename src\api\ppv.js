import request from '@/utils/request'

/**
 * 查询峰平谷尖列表
 *
 */
export function getPpvConfigs(params) {
  return request({
    url: '/saianapi/v1/ppv_configs',
    method: 'get',
    params
  })
}

/**
 * 新增峰平谷尖配置
 *
 */
export function createPpvConfig(data) {
  return request({
    url: '/saianapi/v1/ppv_configs',
    method: 'post',
    data
  })
}

/**
 * 更新峰平谷尖
 *
 */
export function updatePpvConfigs(id, data) {
  return request({
    url: '/saianapi/v1/ppv_configs/' + id,
    method: 'put',
    data
  })
}

/**
 * 删除峰平谷尖
 *
 */
export function deletePpvConfigs(id) {
  return request({
    url: '/saianapi/v1/ppv_configs/' + id,
    method: 'delete'
  })
}
