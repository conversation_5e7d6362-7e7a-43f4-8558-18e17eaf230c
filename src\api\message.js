/*
 * @Author: czf <EMAIL>
 * @Date: 2022-06-20 10:51:57
 * @LastEditors: czf <EMAIL>
 * @LastEditTime: 2022-07-11 17:40:29
 * @FilePath: \ac-web-standard\src\api\message.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import request from '@/utils/request'

/**
 * 获取消息列表
 * query={types}
 */
export function getMessages(query) {
  return request({
    url: '/saianapi/v5/messages',
    method: 'GET',
    params: query
  })
}
/**
 * 获取未读消息列表
 * query={types}
 */
export function getUnreadMessages(query) {
  return request({
    url: '/saianapi/v5/unread_messages',
    method: 'GET',
    params: query
  })
}

/**
 * 标记消息是否已读
 *  msg_ids 需要标记为已读的消息id列表，多个时以逗号分隔，如：1,2
 */
export function messageRead(msg_ids) {
  return request({
    url: '/saianapi/v5/read_messages',
    method: 'POST',
    data: msg_ids
  })
}
/**
 * 用户接收设置消息
 *  msg_types 消息类别列表，多个时以逗号隔开，如：1,2
 */
export function setMessage(msg_types) {
  return request({
    url: '/saianapi/v5/accept_msg_types',
    method: 'POST',
    data: msg_types
  })
}

/**
 * 用户接收设置消息
 */
export function getMessageSettings() {
  return request({
    url: '/saianapi/v5/accept_msg_types',
    method: 'GET'
  })
}

/**
 * 标记消息（有用或没用）
 *  id
 */
export function messageUseful(id) {
  return request({
    url: '/saianapi/v5/messages/' + id,
    method: 'PUT'
  })
}
