/*
 * @Author: czf <EMAIL>
 * @Date: 2023-12-22 10:52:53
 * @LastEditors: czf <EMAIL>
 * @LastEditTime: 2024-01-16 11:51:40
 * @FilePath: \ac-web-standard\src\api\project.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import request from '@/utils/request'
import { getProject } from '@/utils/auth'

/**
 * 获取项目详情
 */
export function getProjectDetail() {
  return request({
    url: '/saianapi/v1/web_projects/' + getProject(),
    method: 'GET'
  })
}

/**
 * 更新项目信息
 * data = { name }
 * name为项目名字
 */
export function updateProject(data) {
  return request({
    url: '/saianapi/v1/web_projects/' + getProject(),
    method: 'PUT',
    data
  })
}

export function isDrf() {
  return request({
    url: '/saianapi/v5/project_settings/is_drf',
    method: 'GET'
  })
}

export function projectSettings() {
  return request({
    url: '/saianapi/v5/project_settings',
    method: 'GET'
  })
}

export function updateProjectSettings(val) {
  const data = {}
  if (val) {
    Object.keys(val).forEach(item => {
      // console.log(item, val[item])
      data[item] = val[item]
    })
  }
  return request({
    url: '/saianapi/v5/project_settings',
    method: 'PUT',
    data: data
  })
}

export function updateProjectSettingsLaunchDate(value) {
  return request({
    url: '/saianapi/v5/project_settings',
    method: 'PUT',
    data: { 'launch_date': value }
  })
}

export function updateProjectSettingsWebStyles(value) {
  return request({
    url: '/saianapi/v5/project_settings',
    method: 'PUT',
    data: { 'web_styles': value }
  })
}
// 更新项目简介

export function updateProjectIntro(data) {
  return request({
    url: '/saianapi/v1/web_projects/' + getProject(),
    method: 'PUT',
    data: data
  })
}

// 获取项目简介
export function getProjectIntro() {
  return request({
    url: '/saianapi/v5/project_info',
    method: 'GET'
  })
}

// 获取节能日历信息
export function getEnergyCalendar(params) {
  return request({
    url: '/saianapi/v5/esr_stats',
    method: 'GET',
    params
  })
}
