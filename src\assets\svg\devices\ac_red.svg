<?xml version="1.0" encoding="utf-8"?>
<svg width="56" height="56" viewBox="0 0 56 56" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>.a{fill:none;}.b{clip-path:url(#a);}.c{fill:url(#b);}</style>
    <clipPath id="a">
      <rect class="a" width="56" height="56" transform="translate(959 212)"/>
    </clipPath>
    <linearGradient id="b" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#fe9a90"/>
      <stop offset="1" stop-color="#f7785a"/>
    </linearGradient>
  </defs>
  <g class="b" transform="translate(-959 -212)">
    <path class="c" d="M 27.34 39.6 C 27.217 39.453 27.148 39.268 27.147 39.076 C 27.161 38.693 27.412 38.36 27.776 38.241 C 29.2 37.686 30.09 36.7 30.09 35.66 C 30.027 34.785 29.525 34.002 28.757 33.579 C 28.708 33.547 28.661 33.51 28.618 33.47 C 27.568 32.814 26.91 31.682 26.858 30.445 C 26.958 28.933 27.898 27.604 29.292 27.008 C 29.755 26.77 30.315 26.828 30.718 27.157 C 30.901 27.314 31.007 27.543 31.01 27.784 C 31.001 28.118 30.805 28.419 30.503 28.562 C 29.716 28.878 29.169 29.602 29.08 30.445 C 29.134 31.154 29.543 31.787 30.167 32.128 L 30.175 32.133 C 30.22 32.163 30.262 32.196 30.302 32.233 C 31.498 32.97 32.251 34.252 32.312 35.656 C 32.312 37.416 30.944 39.045 28.742 39.908 C 28.589 39.967 28.425 39.998 28.261 39.997 C 27.911 40.006 27.574 39.861 27.34 39.6 Z M 20.91 39.6 C 20.787 39.453 20.718 39.268 20.717 39.076 C 20.731 38.693 20.982 38.36 21.346 38.241 C 22.772 37.683 23.659 36.693 23.659 35.657 C 23.596 34.783 23.094 34 22.327 33.576 C 22.276 33.543 22.229 33.505 22.186 33.463 C 21.137 32.808 20.478 31.678 20.427 30.442 C 20.527 28.929 21.468 27.601 22.862 27.005 C 23.325 26.767 23.885 26.825 24.288 27.154 C 24.471 27.311 24.577 27.54 24.58 27.781 C 24.571 28.115 24.375 28.416 24.073 28.559 C 23.286 28.875 22.739 29.599 22.65 30.442 C 22.704 31.151 23.113 31.784 23.737 32.125 C 23.785 32.158 23.83 32.194 23.872 32.234 C 25.069 32.971 25.822 34.253 25.883 35.657 C 25.883 37.417 24.515 39.046 22.312 39.909 C 22.159 39.968 21.995 39.999 21.831 39.998 C 21.481 40.007 21.144 39.861 20.911 39.6 L 20.91 39.6 Z M 14.481 39.6 C 14.358 39.453 14.289 39.268 14.288 39.076 C 14.302 38.693 14.553 38.36 14.917 38.241 C 16.343 37.683 17.23 36.693 17.23 35.657 C 17.166 34.784 16.666 34.003 15.9 33.579 C 15.85 33.546 15.803 33.509 15.759 33.468 C 14.71 32.813 14.052 31.682 14 30.446 C 14.1 28.934 15.04 27.605 16.434 27.009 C 16.897 26.771 17.457 26.829 17.861 27.158 C 18.044 27.315 18.15 27.544 18.153 27.785 C 18.144 28.119 17.948 28.42 17.646 28.563 C 16.859 28.879 16.312 29.603 16.223 30.446 C 16.277 31.155 16.686 31.788 17.31 32.129 L 17.318 32.134 C 17.363 32.164 17.405 32.198 17.445 32.234 C 18.642 32.971 19.395 34.253 19.456 35.657 C 19.456 37.417 18.088 39.046 15.885 39.909 C 15.731 39.97 15.566 40 15.4 40 C 15.05 40.007 14.715 39.861 14.482 39.6 L 14.481 39.6 Z M 40 31 L 7 31 C 3.136 30.996 0.004 27.864 0 24 L 0 8 C 0.004 4.136 3.136 1.004 7 1 L 40 1 C 43.864 1.004 46.996 4.136 47 8 L 47 24 C 46.996 27.864 43.864 30.996 40 31 Z M 29 9 L 29 10 C 29 11.105 29.895 12 31 12 L 40 12 C 41.105 12 42 11.105 42 10 L 42 9 C 42 7.895 41.105 7 40 7 L 31 7 C 29.895 7 29 7.895 29 9 Z" transform="translate(964 221)"/>
  </g>
</svg>