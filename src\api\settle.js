import request from '@/utils/request'

/**
 * 查询结算对象列表
 *
 */
export function getSettleObjects(params) {
  return request({
    url: '/saianapi/v5/settle_parties',
    method: 'get',
    params
  })
}

export function getSettleObjectDetail(id) {
  return request({
    url: '/saianapi/v5/settle_parties/' + id,
    method: 'get'
  })
}

/**
 * 创建结算对象
 *
 */
export function createSettleObject(data) {
  return request({
    url: '/saianapi/v5/settle_parties',
    method: 'post',
    data
  })
}

/**
 * 编辑结算对象
 *
 */
export function updateSettleObject(id, data) {
  return request({
    url: '/saianapi/v5/settle_parties/' + id,
    method: 'put',
    data
  })
}

/**
 * 删除结算对象
 *
 */
export function deleteSettleObject(id) {
  return request({
    url: '/saianapi/v5/settle_parties/' + id,
    method: 'delete'
  })
}

/**
 * 查询结算基准
 *
 */
export function getSettleBasis(params) {
  return request({
    url: '/saianapi/v5/settle_refers',
    method: 'get',
    params
  })
}
export function getSettleBasisDetail(id) {
  return request({
    url: '/saianapi/v5/settle_refers/' + id,
    method: 'get'
  })
}

/**
 * 添加结算基准
 *
 */
export function createSettleBasis(id, data) {
  return request({
    url: '/saianapi/v5/settle_refers?spid=' + id,
    method: 'post',
    data
  })
}
/**
 * 编辑结算基准
 *
 */
export function updateSettleBasis(id, data) {
  return request({
    url: '/saianapi/v5/settle_refers/' + id,
    method: 'put',
    data
  })
}

/**
 * 删除结算基准
 *
 */
export function deleteSettleBasis(id) {
  return request({
    url: '/saianapi/v5/settle_refers/' + id,
    method: 'delete'
  })
}

/**
 * 查询汇总结算记录
 *
 */
export function getSummarySettleRecords(params) {
  return request({
    url: '/saianapi/v5/settle_records',
    method: 'get',
    params
  })
}

/**
 * 查询设备结算记录
 *
 */
export function getDeviceSettleRecords(params) {
  return request({
    url: '/saianapi/v5/devstl_records',
    method: 'get',
    params
  })
}
