import request from '@/utils/request'
import { getProject } from '@/utils/auth'

/**
 * 获取建筑列表
 */
export function getBuildings() {
  return request({
    url: '/saianapi/v1/buildings?project_id=' + getProject(),
    method: 'GET'
  })
}

/**
 * 查询建筑详情
 */
export function getBuilding(id) {
  return request({
    url: '/saianapi/v1/buildings/' + id,
    method: 'GET'
  })
}

/**
 * 添加建筑
 */
export function addBuilding(data) {
  return request({
    url: '/saianapi/v1/buildings',
    method: 'POST',
    params: {
      project_id: getProject()
    },
    data: {
      name: data.name
    }
  })
}

/**
 * 更新建筑
 */
export function updateBuilding(id, data) {
  return request({
    url: '/saianapi/v1/buildings/' + id,
    method: 'PUT',
    data
    // data: {
    //   name: data.name
    // }
  })
}

/**
 * 删除建筑
 * @param {*} id
 */
export function deleteBuilding(id, data) {
  return request({
    url: '/saianapi/v1/buildings/' + id,
    method: 'DELETE'
  })
}

/**
 * 查询楼层列表
 */
export function getFloors(query) {
  const params = {
    project_id: getProject()
  }
  if (query) {
    const { page, per_page, building_id, no_coords_arr } = query
    if (page != null) {
      params.page = query.page
    }
    if (per_page != null) {
      params.per_page = query.per_page
    }
    if (building_id != null) {
      params.building_id = building_id
    }
    if (no_coords_arr != null) {
      params.no_coords_arr = no_coords_arr
    }
  }
  return request({
    url: '/saianapi/v1/floors',
    method: 'GET',
    params
  })
}

/**
 * 查询楼层列表
 */
export function getFloorsByBID(buildingId) {
  return request({
    url: '/saianapi/v1/buildings/' + buildingId + '/floors',
    method: 'GET',
    params: {
      page: 1,
      per_page: 100,
      no_coords_arr: true
    }
  })
}

/**
 * 查询楼层信息
 * @param buildingId
 */
export function getFloorList(buildingId) {
  return request({
    url: '/saianapi/v2/floors',
    method: 'get',
    params: { building_id: buildingId }
  })
}

/**
 * 查询楼层详细信息
 * @param floorId
 */
export function getFloorDetails(floorId) {
  return request({
    url: 'saianapi/v2/floors/' + floorId,
    method: 'get'
  })
}

/**
 * 搜索楼层
 */
export function searchFloors(search) {
  return request({
    url: '/saianapi/v1/floors',
    method: 'GET',
    params: {
      project_id: getProject(),
      search: search
    }
  })
}

/**
 * 查询楼层详情
 */
export function getFloor(buildingId, floorId) {
  return request({
    url: '/saianapi/v1/buildings/' + buildingId + '/floors/' + floorId,
    method: 'GET'
  })
}

/**
 * 添加楼层
 */
export function addFloor(buildingId, data) {
  return request({
    url: '/saianapi/v1/buildings/' + buildingId + '/floors',
    method: 'POST',
    data: {
      name: data.name,
      floor_no: data.floorNo,
      thumb: data.thumb,
      image: data.image
    }
  })
}

/**
 * 更新建筑
 */
export function updateFloor(buildingId, floorId, data) {
  return request({
    url: '/saianapi/v1/buildings/' + buildingId + '/floors/' + floorId,
    method: 'PUT',
    data: {
      name: data.name,
      floor_no: data.floorNo,
      thumb: data.thumb,
      image: data.image
    }
  })
}

/**
 * 删除建筑
 * @param {*} id
 */
export function deleteFloor(buildingId, floorId) {
  return request({
    url: '/saianapi/v1/buildings/' + buildingId + '/floors/' + floorId,
    method: 'DELETE'
  })
}

/**
 * 查询房间列表
 */
export function getRooms(building_id, floor_id) {
  return request({
    url: '/saianapi/v1/buildings/' + building_id + '/floors/' + floor_id + '/active_rooms?project_id=' + getProject(),
    method: 'GET'
  })
}

/**
 * 查询所有房间列表
 * page: 1, per_page: 8
 */
export function getAllRooms(query) {
  const params = Object.assign({}, query, { project_id: getProject() })
  return request({
    url: '/saianapi/v1/active_rooms',
    method: 'GET',
    params
  })
}

/**
 * 查询所有房间列表
 * @param params
 */
export function getSimpleRooms(params) {
  return request({
    url: '/saianapi/v5/active_rooms',
    method: 'GET',
    params
  })
}

/**
 * 查询房间详情
 */
export function getRoom(roomId) {
  return request({
    url: '/saianapi/v1/active_rooms/' + roomId,
    method: 'GET'
  })
}

/**
 * 添加（启用）房间
 * data = { building_id, floor_id, name, room_id, device_ids }
 */
export function addRoom(buildingId, floorId, data) {
  return request({
    url: '/saianapi/v1/buildings/' + buildingId + '/floors/' + floorId + '/active_rooms',
    method: 'POST',
    data: {
      name: data.name,
      min_temp: data.minTemp,
      max_temp: data.maxTemp,
      minHumidity: data.minHumidity,
      max_humidity: data.minHumidity,
      max_tvoc: data.maxTvoc,
      max_co2: data.maxCo2,
      max_pm25: data.maxPm25
    }
  })
}

/**
 * 更新房间信息
 */
export function updateRoom(buildingId, floorId, roomId, data) {
  return request({
    url: '/saianapi/v1/buildings/' + buildingId + '/floors/' + floorId + '/active_rooms/' + roomId,
    method: 'PUT',
    data: {
      name: data.name,
      min_temp: data.minTemp,
      max_temp: data.maxTemp,
      minHumidity: data.minHumidity,
      max_humidity: data.minHumidity,
      max_tvoc: data.maxTvoc,
      max_co2: data.maxCo2,
      max_pm25: data.maxPm25
    }
  })
}

/**
 * 添加普通房间
 * @param {*} buildingId
 * @param {*} floorId
 * @param {*} data
 */
export function addActiveRoom(buildingId, floorId, data) {
  return request({
    url: '/saianapi/v1/buildings/' + buildingId + '/floors/' + floorId + '/active_rooms',
    method: 'POST',
    data: {
      name: data.name,
      min_temp: data.minTemp,
      max_temp: data.maxTemp,
      min_humidity: data.minHumidity,
      max_humidity: data.maxHumidity,
      max_tvoc: data.maxTvoc,
      max_co2: data.maxCo2,
      max_pm25: data.maxPm25,
      user_ids: data.user_ids
    }
  })
}

/**
 * 更新普通房间
 * @param {*} buildingId
 * @param {*} floorId
 * @param {*} roomId
 * @param {*} data
 */
export function updateActiveRoom(buildingId, floorId, roomId, data) {
  return request({
    url: '/saianapi/v1/buildings/' + buildingId + '/floors/' + floorId + '/active_rooms/' + roomId,
    method: 'PUT',
    data: {
      building_id: data.building_id,
      new_floor_id: data.floor_id,
      name: data.name,
      min_temp: data.minTemp,
      max_temp: data.maxTemp,
      min_humidity: data.minHumidity,
      max_humidity: data.maxHumidity,
      max_tvoc: data.maxTvoc,
      max_co2: data.maxCo2,
      max_pm25: data.maxPm25,
      user_ids: data.user_ids,
      active_room_no: data.active_room_no
    }
  })
}

/**
 * 删除普通房间
 * @param {*} buildingId
 * @param {*} floorId
 * @param {*} roomId
 */
export function deleteActiveRoom(buildingId, floorId, roomId) {
  return request({
    url: '/saianapi/v1/buildings/' + buildingId + '/floors/' + floorId + '/active_rooms/' + roomId,
    method: 'DELETE'
  })
}

/**
 * @description 获取房间详情
 * @param {id}
 */
export function getActiveRoom(id) {
  return request({
    url: '/saianapi/v1/active_rooms/' + id,
    method: 'GET'
  })
}
/**
 * 搜索普通房间
 * @param {*} search
 */
export function searchActiveRoom(search) {
  return request({
    url: '/saianapi/v1/active_rooms',
    method: 'GET',
    params: {
      search: search,
      project_id: getProject()
    }
  })
}

/**
 * 绑定房间与设备
 * data = { room_id, device_ids }
 * room_id为房间的id，device_ids为设备id数组，如：[1,2]
 */
export function bindDeviceToRoom(data) {
  return request({
    url: '/saianapi/v1/room_devices',
    method: 'POST',
    data
  })
}

/**
 * 解绑房间与设备
 * data = { room_id, device_ids }
 * room_id为房间的id，device_ids为设备id数组，如：[1,2]
 */
export function unbindDevice(data) {
  return request({
    url: '/saianapi/v1/room_devices/na',
    method: 'DELETE',
    data
  })
}

/**
 * 停用房间
 */
export function deleteRoom(room_id) {
  return request({
    url: '/saianapi/v1/active_rooms/' + room_id,
    method: 'DELETE'
  })
}

/**
 * 房间类型列表
 */
export function getRoomTypeList() {
  return request({
    url: '/saianapi/v1/room_types',
    method: 'GET'
  })
}

/**
 * 获取未启用的房间
 */
export function getUnasignedRooms(floor_id) {
  return request({
    url: '/saianapi/v1/rooms?floor_id=' + floor_id,
    method: 'GET'
  })
}

/**
 * 更新楼层的元素位置信息
 */
export function saveFloorCovers(data) {
  return request({
    url: '/saianapi/v1/buildings/' + data.building + '/floors/' + data.floor,
    method: 'put',
    data: {
      coords_arr: data.coords_arr,
      devices: data.devices
    }
  })
}

/**
 * 根据room id查询报表配置
 */
export function getReports(roomId) {
  return request({
    url: '/saianapi/v1/reports?active_room_id=' + roomId,
    method: 'GET'
  })
}

/**
 * 查询报表
 */
export function getStats(query) {
  return request({
    url: '/saianapi/v2/room_stats',
    method: 'GET',
    params: query
  })
}

/**
 * 保存楼层图的设备管控范围
 */
export function saveDeviceBoundary(data) {
  return request({
    url: '/saianapi/v1/device_boundaries',
    method: 'post',
    data
  })
}

/**
 * 绑定房间相应设备
 * @param {*} room_id 房间id
 * @param {*} device_ids 设备id、数组形式
 * @param {*} terminal_ids
 */
export function saveRoomDevice(data) {
  return request({
    url: '/saianapi/v1/room_devices',
    method: 'post',
    data
  })
}

/**
 * 解除房间与设备的绑定
 * @param {*} id为对应的设备id
 */
export function delRoomDevice(id) {
  return request({
    url: '/saianapi/v1/room_devices/' + id,
    method: 'delete'
  })
}

/**
 * 未绑定房间的设备列表
 * @param {*} id为对应的设备id
 */
export function getUnbindDevice(type) {
  return request({
    url: '/saianapi/v1/web_unbind_devices',
    method: 'get',
    params: {
      project_id: getProject(),
      type: type || 'device'
    }
  })
}

/**
 * 未绑定房间的设备列表
 */
export function getUnbindDeviceOrTerminal(params) {
  return request({
    url: '/saianapi/v1/web_unbind_devices',
    method: 'get',
    params
  })
}

/**
 * 楼层平面图设备状态统计
 */
export function getFloorDeviceStats(params) {
  return request({
    url: '/saianapi/v5/floor_device_stats',
    method: 'get',
    params
  })
}

/**
 * 楼层平面图已绑定设备列表
 */
export function getFloorDeviceList(params) {
  return request({
    url: '/saianapi/v5/floor_device_list',
    method: 'get',
    params
  })
}
