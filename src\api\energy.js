
import request from '@/utils/request'
// 能耗分摊计算

/**
 * 查询能耗仪表
 */
export function getEcMeters(params) {
  return request({
    url: '/saianapi/v5/ec_meters',
    method: 'GET',
    params
  })
}
// 新增能耗仪表
export function createEcMeters(data) {
  return request({
    url: '/saianapi/v5/ec_meters',
    method: 'POST',
    data
  })
}
// 更新能耗仪表
export function updateEcMeters(id, data) {
  return request({
    url: `/saianapi/v5/ec_meters/${id}`,
    method: 'PUT',
    data
  })
}
// 删除能耗仪表
export function deleteEcMeters(id) {
  return request({
    url: `/saianapi/v5/ec_meters/${id}`,
    method: 'DELETE'
  })
}

/**
 * 查询手工录入记录
 */
export function getMetersReadings(params) {
  return request({
    url: '/saianapi/v5/meter_readings',
    method: 'GET',
    params
  })
}

/**
 * 创建手工录入数据
 */
export function createMetersReadings(data) {
  return request({
    url: '/saianapi/v5/meter_readings',
    method: 'POST',
    data
  })
}

/**
 * 修改手工录入数据
 */
export function updateMetersReadings(id, data) {
  return request({
    url: `/saianapi/v5/meter_readings/${id}`,
    method: 'PUT',
    data
  })
}

/**
 * 删除手工录入数据
 */
export function deleteMetersReadings(id) {
  return request({
    url: `/saianapi/v5/meter_readings/${id}`,
    method: 'DELETE'
  })
}

/**
 * 查询运行时间统计信息
 */
export function getRuntimeStats(params) {
  return request({
    url: '/saianapi/v5/runtime_stats',
    method: 'GET',
    params
  })
}

/**
 * 查询设备能耗分摊信息
 */

export function getEcApportion(params) {
  return request({
    url: '/saianapi/v5/ec_apportion',
    method: 'GET',
    params
  })
}

/**
 * 获取设备能耗单价
 */
export function getEcApportionPrice(params) {
  return request({
    url: '/saianapi/v5/project_settings',
    method: 'get',
    params
  })
}

/**
 * 设置能耗单价
 */

export function setEcApportionPrice(data) {
  return request({
    url: '/saianapi/v5/project_settings',
    method: 'put',
    data
  })
}

