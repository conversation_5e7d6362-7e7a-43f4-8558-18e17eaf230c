<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="212.685" height="231.511" viewBox="0 0 212.685 231.511"><defs><style>.a,.af,.ai,.aj,.ak{opacity:0.33;}.a,.af,.ai,.aj,.ak,.ap{mix-blend-mode:multiply;isolation:isolate;}.a{fill:url(#a);}.b{fill:#cac4c4;}.c{fill:url(#b);}.d{fill:url(#c);}.e{fill:url(#d);}.f{fill:url(#e);}.g{fill:url(#f);}.h{fill:url(#g);}.i{fill:url(#h);}.j{fill:url(#i);}.k{fill:url(#j);}.l{fill:url(#k);}.m{fill:url(#l);}.n{fill:url(#m);}.o{fill:#bdb7b7;}.p{fill:#f7f1f1;}.q{fill:url(#n);}.r{fill:url(#o);}.s{fill:url(#p);}.t{fill:url(#q);}.u{fill:url(#r);}.v{fill:url(#s);}.w{fill:url(#t);}.x{fill:url(#u);}.y{fill:url(#v);}.z{fill:url(#w);}.aa{fill:url(#x);}.ab{fill:url(#y);}.ac{fill:#ebe5e5;}.ad{fill:#fff;}.ae{fill:#ececec;}.af{fill:url(#z);}.ag{fill:#787371;}.ah{fill:#d6ccc2;}.ai{fill:url(#aa);}.aj{fill:url(#ab);}.ak{fill:url(#ac);}.al{fill:#dfd9d9;}.am{fill:#9497a4;}.an{fill:#eae4e4;}.ao{fill:#bdc0cd;}.ap{opacity:0.79;}.aq{fill:url(#ad);}.ar{fill:#904c04;}.as{fill:url(#ae);}.at{fill:url(#af);}.au{fill:url(#ag);}.av{fill:url(#ah);}.aw{fill:url(#ai);}.ax{fill:url(#aj);}.ay{fill:url(#ak);}.az{fill:url(#al);}</style><linearGradient id="a" x1="-0.047" y1="0.846" x2="1.064" y2="0.042" gradientUnits="objectBoundingBox"><stop offset="0" stop-color="#333"/><stop offset="1" stop-color="#ccc"/></linearGradient><linearGradient id="b" x1="0.507" y1="0.53" x2="0.802" y2="1.751" gradientUnits="objectBoundingBox"><stop offset="0" stop-color="#4f748f"/><stop offset="1" stop-color="#283640"/></linearGradient><linearGradient id="c" x1="0.48" y1="0.417" x2="0.775" y2="1.638" xlink:href="#b"/><linearGradient id="d" x1="0.453" y1="0.305" x2="0.748" y2="1.525" xlink:href="#b"/><linearGradient id="e" x1="0.425" y1="0.192" x2="0.721" y2="1.412" xlink:href="#b"/><linearGradient id="f" x1="0.398" y1="0.079" x2="0.693" y2="1.299" xlink:href="#b"/><linearGradient id="g" x1="0.371" y1="-0.034" x2="0.666" y2="1.187" xlink:href="#b"/><linearGradient id="h" x1="0.345" y1="-0.142" x2="0.64" y2="1.078" xlink:href="#b"/><linearGradient id="i" x1="0.318" y1="-0.255" x2="0.613" y2="0.966" xlink:href="#b"/><linearGradient id="j" x1="0.29" y1="-0.368" x2="0.585" y2="0.853" xlink:href="#b"/><linearGradient id="k" x1="0.263" y1="-0.48" x2="0.558" y2="0.74" xlink:href="#b"/><linearGradient id="l" x1="0.236" y1="-0.593" x2="0.531" y2="0.627" xlink:href="#b"/><linearGradient id="m" x1="0.208" y1="-0.706" x2="0.504" y2="0.515" xlink:href="#b"/><linearGradient id="n" x1="0.5" y1="0.09" x2="0.5" y2="3.298" gradientUnits="objectBoundingBox"><stop offset="0" stop-color="#74a5c5"/><stop offset="0.984" stop-color="#617a91"/></linearGradient><linearGradient id="o" x1="0.5" y1="-0.154" x2="0.5" y2="3.053" xlink:href="#n"/><linearGradient id="p" x1="0.5" y1="-0.398" x2="0.5" y2="2.81" xlink:href="#n"/><linearGradient id="q" x1="0.5" y1="-0.642" x2="0.5" y2="2.566" xlink:href="#n"/><linearGradient id="r" x1="0.5" y1="-0.885" x2="0.5" y2="2.321" xlink:href="#n"/><linearGradient id="s" x1="0.5" y1="-1.129" x2="0.5" y2="2.078" xlink:href="#n"/><linearGradient id="t" x1="0.5" y1="-1.374" x2="0.5" y2="1.834" xlink:href="#n"/><linearGradient id="u" x1="0.5" y1="-1.617" x2="0.5" y2="1.59" xlink:href="#n"/><linearGradient id="v" x1="0.5" y1="-1.861" x2="0.5" y2="1.346" xlink:href="#n"/><linearGradient id="w" x1="0.5" y1="-2.105" x2="0.5" y2="1.102" xlink:href="#n"/><linearGradient id="x" x1="0.5" y1="-2.349" x2="0.5" y2="0.858" xlink:href="#n"/><linearGradient id="y" x1="0.5" y1="-2.592" x2="0.5" y2="0.614" xlink:href="#n"/><linearGradient id="z" x1="-0.048" y1="0.837" x2="1.012" y2="0.054" xlink:href="#a"/><linearGradient id="aa" x1="-0.081" y1="0.775" x2="1.032" y2="0.051" xlink:href="#a"/><linearGradient id="ab" x1="-0.048" y1="0.837" x2="1.012" y2="0.054" xlink:href="#a"/><linearGradient id="ac" x1="-0.081" y1="0.775" x2="1.032" y2="0.051" xlink:href="#a"/><linearGradient id="ad" x1="0.946" y1="1.173" x2="-0.359" y2="2.388" gradientUnits="objectBoundingBox"><stop offset="0" stop-color="#d8d8d8"/><stop offset="0.929" stop-color="#6f7072"/></linearGradient><radialGradient id="ae" cx="-0.042" cy="0.126" r="1.385" gradientTransform="translate(-0.001 -0.014) scale(1.002 0.97)" gradientUnits="objectBoundingBox"><stop offset="0" stop-color="#7eb832"/><stop offset="0.095" stop-color="#76b22d"/><stop offset="0.241" stop-color="#61a320"/><stop offset="0.417" stop-color="#3e8c0b"/><stop offset="0.5" stop-color="#2c7f00"/><stop offset="1" stop-color="#1a6100"/></radialGradient><radialGradient id="af" cx="-0.141" cy="0.481" r="1.424" gradientTransform="matrix(0.801, -0.525, 0.486, 0.835, 4.535, 3.47)" xlink:href="#ae"/><linearGradient id="ag" x1="0.908" y1="1.256" x2="-0.398" y2="2.471" xlink:href="#ad"/><radialGradient id="ah" cx="-0.042" cy="0.126" r="1.385" gradientTransform="translate(-0.001 -0.012) scale(1.002 0.97)" xlink:href="#ae"/><radialGradient id="ai" cx="-0.141" cy="0.481" r="1.424" gradientTransform="matrix(0.801, -0.525, 0.486, 0.835, 5.17, 3.153)" xlink:href="#ae"/><linearGradient id="aj" x1="0.869" y1="1.333" x2="-0.436" y2="2.548" xlink:href="#ad"/><radialGradient id="ak" cx="-0.042" cy="0.126" r="1.385" gradientTransform="translate(-0.001 -0.011) scale(1.002 0.97)" xlink:href="#ae"/><radialGradient id="al" cx="-0.141" cy="0.481" r="1.424" gradientTransform="matrix(0.801, -0.525, 0.486, 0.835, 5.773, 2.797)" xlink:href="#ae"/></defs><g transform="translate(-649.018 -198.496)"><g transform="translate(649.018 198.496)"><path class="a" d="M1461.978,963.606l-13.544,56.966,66.4.943,71.4-43.155-85.4-49.413Z" transform="translate(-1393.534 -822.749)"/><g transform="translate(0 0)"><g transform="translate(6.997 27.597)"><path class="b" d="M1519.509,827.614l-71.35,42.218V757.072l71.35-42.228Z" transform="translate(-1400.352 -710.722)"/><path class="c" d="M1518.919,721.243l-71.354,42.224v-5.219l71.354-42.226Z" transform="translate(-1399.965 -711.49)"/><path class="d" d="M1518.328,749.36l-71.355,42.228v-5.225l71.355-42.219Z" transform="translate(-1399.578 -729.83)"/><path class="e" d="M1518.919,775.115l-71.354,42.219v-5.218l71.354-42.227Z" transform="translate(-1399.965 -746.619)"/><path class="f" d="M1518.919,799.7l-71.354,42.227v-5.222l71.354-42.222Z" transform="translate(-1399.965 -762.66)"/><path class="g" d="M1517.447,826.943l-71.353,42.225v-5.219l71.353-42.225Z" transform="translate(-1399.005 -780.422)"/><path class="h" d="M1518.328,851.227l-71.355,42.231v-5.231L1518.328,846Z" transform="translate(-1399.578 -796.255)"/><path class="i" d="M1519.509,874.629l-71.35,42.234V911.63l71.35-42.223Z" transform="translate(-1400.352 -811.518)"/><path class="j" d="M1519.509,900.406l-71.35,42.226v-5.225l71.35-42.222Z" transform="translate(-1400.352 -828.329)"/><path class="k" d="M1519.509,926.176l-71.35,42.231V963.18l71.35-42.219Z" transform="translate(-1400.352 -845.139)"/><path class="l" d="M1519.509,951.923l-71.35,42.223v-5.22l71.35-42.226Z" transform="translate(-1400.352 -861.923)"/><path class="m" d="M1519.509,977.693l-71.35,42.224v-5.227l71.35-42.218Z" transform="translate(-1400.352 -878.73)"/><path class="n" d="M1519.509,1003.456l-71.35,42.236v-5.23l71.35-42.219Z" transform="translate(-1400.352 -895.536)"/><g transform="translate(47.807 42.409)"><path class="o" d="M1454.966,937.681l-6.807,3.928V828.848l6.807-3.941Z" transform="translate(-1448.159 -824.908)"/></g><path class="b" d="M1512.729,710.966l-64.57,38.378v-7.974l64.57-38.376Z" transform="translate(-1400.352 -702.995)"/><path class="p" d="M1310.729,741.745l47.807,27.67v-7.973l-47.807-27.67Z" transform="translate(-1310.729 -723.066)"/><path class="o" d="M1640.1,827.614l-7.023,4.059V718.9l7.023-4.054Z" transform="translate(-1520.945 -710.722)"/></g><g transform="translate(0.215 31.723)"><path class="p" d="M1345.823,889.832l-54.589-31.516V745.547l54.589,31.519Z" transform="translate(-1291.234 -734.87)"/><path class="q" d="M1345.823,782.288l-54.589-31.521v-5.22l54.589,31.519Z" transform="translate(-1291.234 -734.87)"/><path class="r" d="M1345.823,808.052l-54.589-31.518V771.31l54.589,31.509Z" transform="translate(-1291.234 -751.672)"/><path class="s" d="M1345.823,833.823,1291.234,802.3v-5.213l54.589,31.507Z" transform="translate(-1291.234 -768.483)"/><path class="t" d="M1345.823,859.581l-54.589-31.516v-5.217l54.589,31.511Z" transform="translate(-1291.234 -785.281)"/><path class="u" d="M1345.823,885.34l-54.589-31.52v-5.227l54.589,31.52Z" transform="translate(-1291.234 -802.07)"/><path class="v" d="M1345.823,911.1l-54.589-31.515v-5.229l54.589,31.516Z" transform="translate(-1291.234 -818.874)"/><path class="w" d="M1345.823,936.872l-54.589-31.518v-5.219l54.589,31.516Z" transform="translate(-1291.234 -835.683)"/><path class="x" d="M1345.823,962.641l-54.589-31.523V925.9l54.589,31.519Z" transform="translate(-1291.234 -852.484)"/><path class="y" d="M1345.823,988.389l-54.589-31.512v-5.226l54.589,31.524Z" transform="translate(-1291.234 -869.279)"/><path class="z" d="M1345.823,1014.158l-54.589-31.517v-5.225l54.589,31.511Z" transform="translate(-1291.234 -886.081)"/><path class="aa" d="M1345.823,1039.921l-54.589-31.516v-5.223l54.589,31.517Z" transform="translate(-1291.234 -902.884)"/><path class="ab" d="M1345.823,1065.687l-54.589-31.522v-5.23l54.589,31.521Z" transform="translate(-1291.234 -919.678)"/><path class="ac" d="M1298.043,862.245l-6.809-3.929V745.547l6.809,3.928Z" transform="translate(-1291.234 -734.87)"/><g transform="translate(47.561 38.137)"><path class="ac" d="M1434.985,941.313l-7.027-4.062V824.488l7.027,4.059Z" transform="translate(-1427.958 -824.488)"/></g><path class="o" d="M1317.618,858.316l-6.811,3.929V749.475l6.811-3.928Z" transform="translate(-1303.998 -734.87)"/><path class="ac" d="M1619.89,831.68l-7.035-4.059V714.856l7.035,4.068Z" transform="translate(-1500.974 -714.856)"/></g><path class="ad" d="M1358.536,689.576l-47.807-27.6,64.573-38.318,47.8,27.6Z" transform="translate(-1303.732 -623.661)"/><path class="ae" d="M1373.883,693.464l-41.353-23.872,55.858-33.147,41.354,23.874Z" transform="translate(-1317.949 -631.998)"/><path class="ae" d="M1619.89,711.183l7.023-4.072-6.81-4.125-7.249,4.129Z" transform="translate(-1500.759 -675.392)"/><path class="ae" d="M1434.985,821.33l7.024-4.064-6.811-4.121-7.24,4.121Z" transform="translate(-1380.181 -747.231)"/><path class="ae" d="M1297.639,741.959l7.026-4.077-6.81-4.122-7.24,4.13Z" transform="translate(-1290.615 -695.461)"/><g transform="translate(64.104 12.618)"><path class="af" d="M1537.783,685.255l-2.7,11.368,13.253.189,8.189-4.385-17.183-9.69Z" transform="translate(-1514.142 -674.804)"/><path class="ad" d="M1495.718,678.851l-20.826-12.022,11.95-6.9,20.825,12.024Z" transform="translate(-1474.892 -659.933)"/><path class="ag" d="M1546.709,697.326l-11.948,6.9v-2.834l11.948-6.894Z" transform="translate(-1513.935 -682.474)"/><path class="ah" d="M1495.718,694.614l-20.826-12.024v-2.832l20.826,12.022Z" transform="translate(-1474.892 -672.862)"/><path class="ai" d="M1532.181,682.454l-1.688,7.083,5.668-.191,5.93-3.4-8.943-5.059Z" transform="translate(-1511.151 -673.596)"/><g transform="translate(5.804 1.761)"><path class="ad" d="M1505.062,676.724l-13.485-7.787L1498.4,665l13.487,7.789Z" transform="translate(-1491.577 -664.995)"/><path class="ag" d="M1537.171,689.838l-6.828,3.944v-2.457l6.828-3.94Z" transform="translate(-1516.858 -679.596)"/><path class="ah" d="M1505.062,686.571l-13.485-7.786v-2.457l13.485,7.787Z" transform="translate(-1491.577 -672.385)"/></g></g><g transform="translate(30.88 31.651)"><path class="aj" d="M1442.281,739.968l-2.7,11.371,13.252.188,8.191-4.386-17.183-9.69Z" transform="translate(-1418.639 -729.518)"/><path class="ad" d="M1400.212,733.568l-20.827-12.022,11.953-6.9,20.825,12.028Z" transform="translate(-1379.384 -714.647)"/><path class="ag" d="M1451.207,752.05l-11.95,6.9v-2.831l11.95-6.893Z" transform="translate(-1418.429 -737.196)"/><path class="ah" d="M1400.212,749.331l-20.827-12.023v-2.83l20.827,12.022Z" transform="translate(-1379.384 -727.58)"/><path class="ak" d="M1436.678,737.173l-1.685,7.083,5.667-.191,5.932-3.4-8.944-5.058Z" transform="translate(-1415.649 -728.314)"/><g transform="translate(5.806 1.763)"><path class="ad" d="M1409.56,731.443l-13.487-7.786,6.828-3.942,13.485,7.788Z" transform="translate(-1396.074 -719.714)"/><path class="ag" d="M1441.669,744.556l-6.826,3.942v-2.455l6.826-3.94Z" transform="translate(-1421.357 -734.314)"/><path class="ah" d="M1409.56,741.287l-13.487-7.785v-2.456l13.487,7.786Z" transform="translate(-1396.074 -727.104)"/></g></g><g transform="translate(0.206 144.495)"><g transform="translate(54.607)"><path class="al" d="M1448.184,1090.387l71.316-42.032v-9.316l-71.316,42.037Z" transform="translate(-1448.184 -1039.038)"/></g><g transform="translate(54.607 3.089)"><path class="am" d="M1448.184,1098.027l71.316-42.038v-8.071l-71.316,42.034Z" transform="translate(-1448.184 -1047.918)"/></g><g transform="translate(0 10.554)"><path class="an" d="M1291.206,1078.69l54.608,31.521v-9.321l-54.608-31.511Z" transform="translate(-1291.206 -1069.379)"/><g transform="translate(0 3.088)"><path class="ao" d="M1291.206,1086.324l54.608,31.516v-8.074l-54.608-31.51Z" transform="translate(-1291.206 -1078.255)"/></g></g></g></g></g><g transform="translate(703.771 329.409)"><g transform="translate(94.19 0)"><g class="ap" transform="translate(21.873 31.157)"><path class="aq" d="M1815.9,1108.654c-6.308,2.361-14.419.966-19.173-3.073-5.23,1.972-12.9,4.86-13.451,5.018a1.7,1.7,0,0,1-1.26-.561.935.935,0,0,1-.238-1.177c.292-.126,3.218-1.223,6.645-2.507-.036-.739-.093-1.91-.148-3.236a12.5,12.5,0,0,1-1.437-.461,9.918,9.918,0,0,1-3.551-2.275c-1.732-1.8-2.167-4.031-.86-5.878,1.967-2.786,7.137-3.649,11.541-1.925a11.857,11.857,0,0,1,1.563.746,13.588,13.588,0,0,1,4.445-2.606c6.79-2.543,16.433-.758,21.015,3.992.16.167.317.338.463.514C1825.832,1100.357,1822.927,1106.023,1815.9,1108.654Zm-25.335-3.1c1.545-.573,3.1-1.155,4.5-1.676-.193-.234-.374-.474-.535-.713a11.8,11.8,0,0,1-4.05.329c.034.761.066,1.467.088,2.06Z" transform="translate(-1781.653 -1089.561)"/></g><g transform="translate(21.524 27.351)"><path class="ar" d="M1783.947,1103.093c-.222.777-2.36,1.151-3.267-.167-.068-1.019,0-24.306,0-24.306l3.267.169S1784.049,1102.057,1783.947,1103.093Z" transform="translate(-1780.65 -1078.619)"/></g><g transform="translate(5.028 30.575)"><path class="ar" d="M1752.267,1098.878c.48.489-.069,1.976-1.376,1.941-.766-.418-17.662-10.991-17.662-10.991l1.376-1.942S1751.555,1098.346,1752.267,1098.878Z" transform="translate(-1733.229 -1087.886)"/></g><g transform="translate(5.438)"><path class="as" d="M1768.4,1018.112c0,9.329-7.792,16.458-17.426,15.9s-16.564-7.918-16.564-17.246a17.033,17.033,0,0,1,17.846-16.741C1761.884,1000.586,1768.4,1008.764,1768.4,1018.112Z" transform="translate(-1734.407 -999.994)"/></g><g transform="translate(0 23.066)"><path class="at" d="M1737.62,1071.009a9.184,9.184,0,0,1-3.417,13.088,10.6,10.6,0,0,1-13.951-3.375,9.18,9.18,0,0,1,3.411-13.093A10.6,10.6,0,0,1,1737.62,1071.009Z" transform="translate(-1718.775 -1066.3)"/></g></g><g transform="translate(46.121 22.735)"><g class="ap" transform="translate(21.872 31.157)"><path class="au" d="M1677.715,1174.01c-6.309,2.365-14.419.967-19.173-3.073-5.229,1.974-12.9,4.861-13.448,5.019a1.714,1.714,0,0,1-1.263-.566.93.93,0,0,1-.238-1.173c.292-.127,3.219-1.223,6.646-2.506-.036-.737-.093-1.914-.148-3.24a13.121,13.121,0,0,1-1.436-.456,10.018,10.018,0,0,1-3.551-2.276c-1.732-1.8-2.168-4.032-.861-5.881,1.968-2.786,7.137-3.649,11.543-1.924a11.512,11.512,0,0,1,1.561.748,13.53,13.53,0,0,1,4.446-2.608c6.79-2.545,16.431-.759,21.015,3.994.16.168.315.337.465.511C1687.649,1165.713,1684.743,1171.38,1677.715,1174.01Zm-25.334-3.1c1.544-.578,3.1-1.156,4.5-1.677-.193-.235-.375-.475-.536-.713a11.959,11.959,0,0,1-4.049.33c.033.759.067,1.467.088,2.06Z" transform="translate(-1643.469 -1154.916)"/></g><g transform="translate(21.524 27.352)"><path class="ar" d="M1645.764,1168.452c-.223.778-2.36,1.152-3.268-.169-.067-1.017,0-24.3,0-24.3l3.268.166S1645.864,1167.416,1645.764,1168.452Z" transform="translate(-1642.467 -1143.979)"/></g><g transform="translate(5.027 30.577)"><path class="ar" d="M1614.083,1164.24c.478.488-.068,1.978-1.376,1.939-.765-.416-17.664-10.991-17.664-10.991l1.379-1.939S1613.371,1163.71,1614.083,1164.24Z" transform="translate(-1595.044 -1153.25)"/></g><g transform="translate(5.438)"><path class="av" d="M1630.215,1083.469c0,9.331-7.792,16.458-17.424,15.9s-16.565-7.912-16.565-17.242a17.033,17.033,0,0,1,17.845-16.743C1623.7,1065.94,1630.215,1074.119,1630.215,1083.469Z" transform="translate(-1596.226 -1065.35)"/></g><g transform="translate(0 23.067)"><path class="aw" d="M1599.438,1136.371a9.177,9.177,0,0,1-3.418,13.083,10.593,10.593,0,0,1-13.95-3.375,9.176,9.176,0,0,1,3.412-13.089A10.592,10.592,0,0,1,1599.438,1136.371Z" transform="translate(-1580.593 -1131.661)"/></g></g><g transform="translate(0 48.07)"><g class="ap" transform="translate(21.873 31.155)"><path class="ax" d="M1545.133,1246.834c-6.308,2.365-14.419.967-19.172-3.073-5.229,1.974-12.9,4.86-13.449,5.019a1.7,1.7,0,0,1-1.262-.565.93.93,0,0,1-.237-1.175c.29-.127,3.218-1.223,6.646-2.505-.038-.74-.1-1.915-.148-3.239a13.17,13.17,0,0,1-1.438-.458,9.986,9.986,0,0,1-3.55-2.277c-1.732-1.795-2.168-4.031-.861-5.879,1.968-2.785,7.137-3.649,11.542-1.924a11.362,11.362,0,0,1,1.563.749,13.529,13.529,0,0,1,4.445-2.609c6.79-2.546,16.431-.757,21.016,3.994.16.168.315.337.463.511C1555.067,1238.538,1552.161,1244.206,1545.133,1246.834Zm-25.335-3.1c1.546-.578,3.1-1.157,4.5-1.679-.192-.233-.373-.473-.533-.711a11.894,11.894,0,0,1-4.05.329c.034.76.066,1.467.087,2.061Z" transform="translate(-1510.887 -1227.74)"/></g><g transform="translate(21.525 27.351)"><path class="ar" d="M1513.185,1241.278c-.222.777-2.361,1.151-3.266-.17-.069-1.016,0-24.3,0-24.3l3.266.166S1513.286,1240.241,1513.185,1241.278Z" transform="translate(-1509.888 -1216.804)"/></g><g transform="translate(5.028 30.576)"><path class="ar" d="M1481.5,1237.065c.481.491-.068,1.978-1.376,1.939-.765-.416-17.661-10.989-17.661-10.989l1.377-1.94S1480.791,1236.533,1481.5,1237.065Z" transform="translate(-1462.464 -1226.075)"/></g><g transform="translate(5.439)"><path class="ay" d="M1497.633,1156.3c0,9.331-7.791,16.46-17.425,15.9s-16.564-7.913-16.564-17.244a17.032,17.032,0,0,1,17.844-16.74C1491.12,1138.766,1497.633,1146.947,1497.633,1156.3Z" transform="translate(-1463.644 -1138.179)"/></g><g transform="translate(0 23.067)"><path class="az" d="M1466.856,1209.2a9.182,9.182,0,0,1-3.418,13.085,10.6,10.6,0,0,1-13.951-3.376,9.177,9.177,0,0,1,3.412-13.089A10.6,10.6,0,0,1,1466.856,1209.2Z" transform="translate(-1448.01 -1204.488)"/></g></g></g></g></svg>