// 关于设备分组和快捷操作的api

/* 设备分组相关接口 */
import request from '@/utils/request'

// 分组列表
export function getGroup(params) {
  return request({
    url: `/saianapi/v1/groups`,
    method: 'get',
    params
  })
}

// 添加分组
export function addGroup(data) {
  return request({
    url: `/saianapi/v1/groups`,
    method: 'post',
    data
  })
}
// 更新分组
export function updateGroup(id, data) {
  return request({
    url: `/saianapi/v1/groups/${id}`,
    method: 'put',
    data
  })
}
// 分组详情
export function getGroupDetail(id) {
  return request({
    url: `/saianapi/v1/groups/${id}`,
    method: 'get'
  })
}

// 删除分组
export function deleteGroup(id) {
  return request({
    url: `/saianapi/v1/groups/${id}`,
    method: 'delete'
  })
}

/*  快捷操作 */

// 查询快捷操作列表
export function getGroupActions(params) {
  return request({
    url: `/saianapi/v1/group_actions`,
    method: 'get',
    params
  })
}
// 新增快捷操作
export function addGroupActions(data) {
  return request({
    url: `/saianapi/v1/group_actions`,
    method: 'post',
    data
  })
}
// 查询快捷操作详情
export function getGroupActionsDetail(id) {
  return request({
    url: `/saianapi/v1/group_actions/${id}`,
    method: 'get'
  })
}
// 修改快捷操作
export function updateGroupActions(data, id) {
  return request({
    url: `/saianapi/v1/group_actions/${id}`,
    method: 'put',
    data
  })
}
// 删除快捷操作
export function deleteGroupActions(id) {
  return request({
    url: `/saianapi/v1/group_actions/${id}`,
    method: 'delete'
  })
}
// 执行快捷操作
export function executeActions(data) {
  return request({
    url: `/saianapi/v1/execute_actions`,
    method: 'post',
    data
  })
}

/* 定时器部分 */

// 查询定时器列表
export function getTimers(params) {
  return request({
    url: `/saianapi/v1/action_timers`,
    method: 'get',
    params
  })
}

// 新增定时器
export function addTimer(data, id) {
  return request({
    url: `/saianapi/v1/group_actions/${id}/action_timers`,
    method: 'post',
    data
  })
}
// 修改定时器
export function updateTimer(data, group_action_id, timer_id) {
  return request({
    url: `/saianapi/v1/group_actions/${group_action_id}/action_timers/${timer_id}`,
    method: 'put',
    data
  })
}
// 定时器详情
export function getTimerDetail(id, timerId) {
  return request({
    url: `/saianapi/v1/group_actions/${id}/action_timers/${timerId}`,
    method: 'get'
  })
}
// 删除定时器
export function deleteTimer(group_action_id, timer_id) {
  return request({
    url: `/saianapi/v1/group_actions/${group_action_id}/action_timers/${timer_id}`,
    method: 'delete'
  })
}

/* 跨类型关联参数 */
// 关联参数列表
export function getAttributes(params) {
  return request({
    url: `/saianapi/v1/cross_attributes`,
    method: 'get',
    params
  })
}

// 新增关联参数
export function addAttribute(data) {
  return request({
    url: `/saianapi/v1/cross_attributes`,
    method: 'post',
    data
  })
}
// 更新关联参数
export function updateAttribute(data, id) {
  return request({
    url: `/saianapi/v1/cross_attributes/${id}`,
    method: 'put',
    data
  })
}
// 删除关联参数
export function deleteAttribute(id) {
  return request({
    url: `/saianapi/v1/cross_attributes/${id}`,
    method: 'delete'
  })
}

export function getAttrProto(params) {
  // params.read_only = 0
  return request({
    url: `/saianapi/v1/attribute_prototypes`,
    method: 'get',
    params
  })
}

// 查询设备分组终端列表
export function getTerminals(params) {
  return request({
    url: `/saianapi/v5/terminals`,
    method: 'get',
    params
  })
}

// 设备分组优化、快捷操作优化-v5

// 分组列表
export function getGroupV5(params) {
  return request({
    url: `/saianapi/v5/groups`,
    method: 'get',
    params
  })
}

// 查询快捷操作列表
export function getActionV5(params) {
  return request({
    url: `/saianapi/v5/shortcuts`,
    method: 'get',
    params
  })
}
// 查询快捷操作详情
export function getActionDetailsV5(id) {
  return request({
    url: `/saianapi/v5/shortcuts/${id}`,
    method: 'get'
  })
}

// 执行快捷操作
export function executeActionV5(data) {
  return request({
    url: `/saianapi/v5/execute_shortcuts`,
    method: 'post',
    data
  })
}

// 查询执行记录列表
export function getActionLogsList(params) {
  return request({
    url: `/saianapi/v5/action_logs`,
    method: 'get',
    params
  })
}

// 查询执行记录详情
export function getLogsDetail(params) {
  return request({
    url: `/saianapi/v1/device_ctrl_logs`,
    method: 'get',
    params
  })
}

// 查询执行进度
export function getCtrlLog() {
  return request({
    url: `/saianapi/v1/device_ctrl_logs`,
    method: 'get',
    params: {
      exec: 1
    }
  })
}

