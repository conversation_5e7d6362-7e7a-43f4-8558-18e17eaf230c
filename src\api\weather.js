/*
 * @Author: czf <EMAIL>
 * @Date: 2023-11-09 10:10:34
 * @LastEditors: czf <EMAIL>
 * @LastEditTime: 2023-11-15 17:44:22
 * @FilePath: \ac-web-standard\src\api\weather.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import request from '@/utils/request'
import { getProject } from '@/utils/auth'

/**
 * 获取天气信息
 * project_id为项目id
 */
export function getWeather() {
  return request({
    url: '/saianapi/v1/weather/' + getProject(),
    method: 'GET'
  })
}

/**
 * 查询天气
 * @param {form, to,type} params
 * @returns
 */
export function getProjectWeather(params) {
  return request({
    url: '/saianapi/v1/project_weather',
    method: 'GET',
    params
  })
}
