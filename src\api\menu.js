import request from '@/utils/request'

/**
 * 获取菜单列表
 * @param {Object} params
 */
export function getMenus(params) {
  return request({
    url: '/saianapi/v1/web_menus',
    method: 'get',
    params
  })
}

/**
 * 更新菜单
 * @param menuId 资源ID
 * @param data
 */
export function updateMenus(menuId, data) {
  return request({
    url: '/saianapi/v1/web_menus/' + menuId,
    method: 'put',
    data
  })
}

/**
 * 创建菜单
 * @param data
 */
export function createMenu(data) {
  return request({
    url: '/saianapi/v1/web_menus',
    method: 'post',
    data
  })
}

/**
 * 删除菜单
 * @param menuId
 */
export function deleteMenu(menuId) {
  return request({
    url: '/saianapi/v1/web_menus/' + menuId,
    method: 'delete'
  })
}

/** 查询菜单详情
 * @param menuId
 */
export function getMenuDetail(menuId) {
  return request({
    url: '/saianapi/v1/web_menus/' + menuId,
    method: 'get'
  })
}
