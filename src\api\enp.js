import request from '@/utils/request'

export function getNameplates(params) {
  return request({
    url: `/saianapi/v5/nameplates`,
    method: 'get',
    params
  })
}

export function createNameplates(data) {
  return request({
    url: `/saianapi/v5/nameplates`,
    method: 'post',
    data
  })
}

export function updateNameplates(data) {
  return request({
    url: `/saianapi/v5/nameplates`,
    method: 'put',
    data
  })
}

export function getNameplatesDetail(id) {
  return request({
    url: `/saianapi/v5/nameplates`,
    method: 'get'
  })
}

export function deleteNameplates(data) {
  return request({
    url: `/saianapi/v5/nameplates`,
    method: 'delete',
    data
  })
}
