import request from '@/utils/request'
/*
export function getManual() {
  return request({
    url: '/saianapi/v1/web_manuals/2',
    method: 'get'
  })
}

export function updateManual(content) {
  return request({
    url: '/saianapi/v1/web_manuals/2',
    method: 'put',
    data: { content }
  })
}
 */

export function setManual(params) {
  return request({
    url: '/saianapi/v1/web_manuals',
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded' // 表单提交
    },
    transformRequest: [function(data) {
      // Do whatever you want to transform the data
      let ret = ''
      for (const it in data) {
        ret += encodeURIComponent(it) + '=' + encodeURIComponent(data[it]) + '&'
      }
      return ret
    }],
    data: params
  })
}
export function updateManual(params, id) {
  return request({
    url: '/saianapi/v1/web_manuals/' + id,
    method: 'PUT',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded' //
    },
    transformRequest: [function(data) {
      let ret = ''
      for (const it in data) {
        ret += encodeURIComponent(it) + '=' + encodeURIComponent(data[it]) + '&'
      }
      return ret
    }],
    data: params
  })
}

/**
 * 上传用户说明
 * @param file
 * @param headers
 * @returns {*}
 */
export function uploadWebManual(file, headers) {
  return request({
    url: '/saianapi/v1/web_manuals',
    method: 'post',
    headers,
    data: { file }
  })
}
