import request from '@/utils/request'

// 末端温度设定

/** 列表
 * */
export function getDeviceLimits(params) {
  return request({
    url: '/saianapi/v5/device_limits',
    method: 'get',
    params
  })
}

/** 新增
 * */
export function createDeviceLimit(data) {
  return request({
    url: '/saianapi/v5/device_limits',
    method: 'post',
    data
  })
}
/** 修改
 * */
export function updateDeviceLimit(id, data) {
  return request({
    url: '/saianapi/v5/device_limits/' + id,
    method: 'put',
    data
  })
}
/** 删除
 * */
export function deleteDeviceLimit(id) {
  return request({
    url: '/saianapi/v5/device_limits/' + id,
    method: 'delete'
  })
}

export function getAttrProto(params) {
  // params.read_only = 0
  return request({
    url: `/saianapi/v1/attribute_prototypes`,
    method: 'get',
    params
  })
}
