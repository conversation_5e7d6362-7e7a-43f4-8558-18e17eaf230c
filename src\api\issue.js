import request from '@/utils/request'
import { getProject } from '@/utils/auth'

/**
 * 获取报警列表
 * @param { Number } page
 * @param { Number } per_page
 */
export function getIssues(query) {
  const params = Object.assign({}, query, { project_id: getProject() })
  return request({
    url: '/saianapi/v1/web_issues',
    method: 'GET',
    params
  })
}

/**
 * 获取dashboard的报警故障统计
 * @param { Number } day
 * @param { String } type
 */
export function getIssueStats(query) {
  return request({
    url: '/saianapi/v2/issue_stats',
    method: 'GET',
    params: {
      day: query.day,
      type: query.type,
      project_id: getProject()
    }
  })
}

/**
 * 清除故障告警或添加屏蔽规则，days=0时添加屏蔽规则
 * days=0表示是永久屏蔽
 * query { device_id, issue_name, days }
 */
/* export function cleanIssue(query) {
  return request({
    url: '/saianapi/v1/issue_whitelists',
    method: 'post',
    params: query
  })
} */
// 查寻所有的屏蔽列表
export function getIssueRules() {
  return request({
    url: '/saianapi/v1/issue_whitelists',
    method: 'get'
  })
}
// 查询屏蔽列表中某一条的详情
export function getIssueRuleDetial(id) {
  return request({
    url: '/saianapi/v1/issue_whitelists/' + id,
    method: 'get'
  })
}

/**
 * 新建屏蔽规则
 * device_id
 * issue_name
 * days，屏蔽天数，0-永久屏蔽
 * */
export function addIssueRule(data) {
  return request({
    url: '/saianapi/v1/issue_whitelists',
    method: 'post',
    data: {
      device_id: data.deviceId,
      days: data.days,
      issue_name: data.name
    }
  })
}

/**
 * 把故障报警从屏蔽规则中移除
 * whiteListId为屏蔽规则id
 */
export function removeIssueRule(id) {
  return request({
    url: '/saianapi/v1/issue_whitelists/' + id,
    method: 'delete'
  })
}

/**
 * 修改屏蔽规则中的信息
 */
export function updateIssues(data, id) {
  return request({
    url: '/saianapi/v1/issue_whitelists/' + id,
    method: 'put',
    data: {
      device_id: data.deviceId,
      days: data.days,
      issue_name: data.name
    }
  })
}

/**
 * 查询故障描述列表
 * protoid - 设备类型id，issueType，10-故障，20-报警
 * @param { Number } protoid
 * @param { Number } issueType
 */
export function getIssueGlossaries(query) {
  return request({
    url: '/saianapi/v1/issue_glossaries',
    method: 'get',
    params: query
  })
}
