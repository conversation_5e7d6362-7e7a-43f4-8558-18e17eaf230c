import request from '@/utils/request'
// 通知管理

/**
 * 查询配置列表
 */
export function getNotifyConfigs(params) {
  return request({
    url: '/saianapi/v5/notify_configs',
    method: 'GET',
    params
  })
}
// 新增配置
export function createNotifyConfigs(data) {
  return request({
    url: '/saianapi/v5/notify_configs',
    method: 'POST',
    data
  })
}

// 查询配置详情
export function getNotifyConfigsDetail(id) {
  return request({
    url: '/saianapi/v5/notify_configs/' + id,
    method: 'GET'

  })
}

// 更新配置
export function updateNotifyConfigs(id, data) {
  return request({
    url: '/saianapi/v5/notify_configs/' + id,
    method: 'PUT',
    data
  })
}
// 删除配置
export function deleteNotifyConfigs(id) {
  return request({
    url: '/saianapi/v5/notify_configs/' + id,
    method: 'DELETE'
  })
}

/**
 * 通知记录列表
 */
export function getNotifyHistories(params) {
  return request({
    url: '/saianapi/v5/notify_histories',
    method: 'GET',
    params
  })
}

/**
 * 通知超限配置-列表
 */
export function getNotifyLimits(params) {
  return request({
    url: '/saianapi/v5/notify_limits',
    method: 'GET',
    params
  })
}

/**
 * 通知超限配置-添加
 */
export function createNotifyLimits(data) {
  return request({
    url: '/saianapi/v5/notify_limits',
    method: 'post',
    data
  })
}
/**
 * 通知超限配置-修改
 */
export function updateNotifyLimits(id, data) {
  return request({
    url: '/saianapi/v5/notify_limits/' + id,
    method: 'put',
    data
  })
}
/**
 * 通知超限配置-删除
 */
export function deleteNotifyLimits(id) {
  return request({
    url: '/saianapi/v5/notify_limits/' + id,
    method: 'delete'
  })
}

/**
 * 分组列表
 */
export function getGroups(params) {
  return request({
    url: '/saianapi/v1/groups',
    method: 'get',
    params
  })
}
