/*
 * @Author: czf <EMAIL>
 * @Date: 2023-01-03 14:57:31
 * @LastEditors: czf <EMAIL>
 * @LastEditTime: 2023-01-03 15:21:26
 * @FilePath: \ac-web-standard\src\api\strategies.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import request from '@/utils/request'
// import { getProject } from '@/utils/auth'

/**
 * 查询运行策略列表
 *
 */
export function getStrategies(params) {
  return request({
    url: '/saianapi/v5/strategies',
    method: 'get',
    params
  })
}

/**
 * 查询运行策略详情
 *
 */
export function getStrategiesDetail(id) {
  return request({
    url: '/saianapi/v5/strategies/' + id,
    method: 'get'
  })
}

/**
 * 创建任务
 *
 */
export function createStrategies(data) {
  return request({
    url: '/saianapi/v5/strategies',
    method: 'post',
    data
  })
}

/**
 * 更新运行策略
 *
 */
export function updateStrategies(id, data) {
  return request({
    url: '/saianapi/v5/strategies/' + id,
    method: 'put',
    data
  })
}

/**
 * 删除运行策略
 *
 */
export function deleteStrategies(id) {
  return request({
    url: '/saianapi/v5/strategies/' + id,
    method: 'delete'
  })
}
