import request from '@/utils/request'

export function getAllEcTranscription() {
  return request({
    url: '/saianapi/v1/ec_transcription',
    method: 'get'
  })
}

export function createEcTranscription(data) {
  return request({
    url: '/saianapi/v1/ec_transcription',
    method: 'post',
    data
  })
}

export function deleteEcTranscription(id) {
  return request({
    url: '/saianapi/v1/ec_transcription/' + id,
    method: 'delete'
  })
}

export function updateEcTranscription(id, data) {
  return request({
    url: '/saianapi/v1/ec_transcription/' + id,
    method: 'put',
    data
  })
}
