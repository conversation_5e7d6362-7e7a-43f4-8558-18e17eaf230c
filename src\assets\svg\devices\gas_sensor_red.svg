<?xml version="1.0" encoding="utf-8"?>
<svg width="56" height="56" viewBox="0 0 56 56" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <style>.a{fill:url(#a);}.b{clip-path:url(#b);}.c{fill:url(#c);}.d{fill:url(#d);}</style>
    <linearGradient id="a" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#fe9a90"/>
      <stop offset="1" stop-color="#f7785a"/>
    </linearGradient>
    <clipPath id="b">
      <rect class="a" width="56" height="56" transform="translate(-512 1638)"/>
    </clipPath>
    <linearGradient id="c" y1="-0.357" x2="0.552" y2="2.08" xlink:href="#a"/>
    <linearGradient id="d" x1="-1.184" y1="7.31" x2="3.879" y2="7.518" xlink:href="#a"/>
  </defs>
  <g class="b" transform="translate(512 -1638)">
    <g transform="translate(0 3)">
      <path class="c" d="M -494.028 1647 C -497.086 1646.951 -498.945 1643.611 -497.374 1640.987 C -496.668 1639.807 -495.402 1639.077 -494.028 1639.055 L -490.383 1639.055 C -490.63 1636.895 -488.941 1635.001 -486.767 1635 L -480.282 1635 C -478.108 1635.001 -476.419 1636.895 -476.666 1639.055 L -473.5 1639.055 C -470.442 1639.104 -468.583 1642.444 -470.154 1645.068 C -470.86 1646.248 -472.126 1646.978 -473.5 1647 L -494.028 1647 Z"/>
      <g transform="translate(-504.145 1672.176)">
        <g transform="translate(0 0)">
          <path class="d" d="M6.61,0C5.7,0,4.843.678,4.189,1.913a.519.519,0,0,0-.061.094c-.448.882-1.041,1.368-1.673,1.368-.724,0-1.4-.643-1.852-1.764-.113-.28-.324-.333-.472-.118a1.064,1.064,0,0,0-.063.9C.651,3.826,1.522,4.651,2.456,4.651A2.588,2.588,0,0,0,4.559,3,.452.452,0,0,0,4.619,2.9C5.152,1.855,5.858,1.278,6.61,1.278c.993,0,1.924,1.052,2.43,2.747.091.307.3.414.459.242A1,1,0,0,0,9.626,3.4C9,1.3,7.845,0,6.61,0Z" transform="translate(11.702 0.64) rotate(90)"/>
          <path class="d" d="M7.25,0A2.985,2.985,0,0,1,9.466,1.128,7.24,7.24,0,0,1,10.88,3.853a1.617,1.617,0,0,1-.272,1.491.865.865,0,0,1-.634.287.972.972,0,0,1-.908-.782C8.657,3.48,7.927,2.56,7.25,2.56c-.49,0-1.006.464-1.418,1.272a1.144,1.144,0,0,1-.094.156A3.159,3.159,0,0,1,3.1,5.933C1.887,5.933.8,4.962.115,3.269a1.7,1.7,0,0,1,.128-1.5.891.891,0,0,1,.733-.41.943.943,0,0,1,.862.652c.34.84.822,1.362,1.258,1.362.372,0,.773-.371,1.1-1.017a1.232,1.232,0,0,1,.09-.149A3.55,3.55,0,0,1,7.25,0Z" transform="translate(12.343 0) rotate(90)"/>
          <path class="d" d="M6.61,0C5.7,0,4.843.678,4.189,1.913a.552.552,0,0,0-.061.094c-.448.882-1.041,1.368-1.673,1.368-.724,0-1.4-.642-1.852-1.764-.113-.28-.324-.333-.472-.119a1.064,1.064,0,0,0-.063.9C.651,3.826,1.522,4.651,2.456,4.651A2.588,2.588,0,0,0,4.559,3,.458.458,0,0,0,4.619,2.9C5.152,1.855,5.858,1.278,6.61,1.278c.993,0,1.924,1.052,2.43,2.747.091.307.3.414.459.242A1,1,0,0,0,9.626,3.4C9,1.3,7.845,0,6.61,0Z" transform="translate(5.292 0.64) rotate(90)"/>
          <path class="d" d="M7.25,0A2.985,2.985,0,0,1,9.466,1.128,7.24,7.24,0,0,1,10.88,3.853a1.617,1.617,0,0,1-.272,1.491.865.865,0,0,1-.634.287.972.972,0,0,1-.908-.782C8.657,3.48,7.927,2.56,7.25,2.56c-.49,0-1.006.464-1.418,1.272a1.15,1.15,0,0,1-.093.156A3.159,3.159,0,0,1,3.1,5.933C1.887,5.933.8,4.962.115,3.269a1.7,1.7,0,0,1,.128-1.5.891.891,0,0,1,.733-.41.943.943,0,0,1,.862.652c.34.84.822,1.362,1.258,1.362.372,0,.773-.371,1.1-1.017l0-.01a1.257,1.257,0,0,1,.085-.14A3.549,3.549,0,0,1,7.25,0Z" transform="translate(5.933 0) rotate(90)"/>
        </g>
        <g transform="translate(28.424 0)">
          <path class="d" d="M6.61,0C5.7,0,4.843.678,4.189,1.913a.519.519,0,0,0-.061.094c-.448.882-1.041,1.368-1.673,1.368-.724,0-1.4-.643-1.852-1.764-.113-.28-.324-.333-.472-.118a1.064,1.064,0,0,0-.063.9C.651,3.826,1.522,4.651,2.456,4.651A2.588,2.588,0,0,0,4.559,3,.452.452,0,0,0,4.619,2.9C5.152,1.855,5.858,1.278,6.61,1.278c.993,0,1.924,1.052,2.43,2.747.091.307.3.414.459.242A1,1,0,0,0,9.626,3.4C9,1.3,7.845,0,6.61,0Z" transform="translate(11.702 0.64) rotate(90)"/>
          <path class="d" d="M7.25,0A2.985,2.985,0,0,1,9.466,1.128,7.24,7.24,0,0,1,10.88,3.853a1.617,1.617,0,0,1-.272,1.491.865.865,0,0,1-.634.287.972.972,0,0,1-.908-.782C8.657,3.48,7.927,2.56,7.25,2.56c-.49,0-1.006.464-1.418,1.272a1.144,1.144,0,0,1-.094.156A3.159,3.159,0,0,1,3.1,5.933C1.887,5.933.8,4.962.115,3.269a1.7,1.7,0,0,1,.128-1.5.891.891,0,0,1,.733-.41.943.943,0,0,1,.862.652c.34.84.822,1.362,1.258,1.362.372,0,.773-.371,1.1-1.017a1.232,1.232,0,0,1,.09-.149A3.55,3.55,0,0,1,7.25,0Z" transform="translate(12.343 0) rotate(90)"/>
          <path class="d" d="M6.61,0C5.7,0,4.843.678,4.189,1.913a.552.552,0,0,0-.061.094c-.448.882-1.041,1.368-1.673,1.368-.724,0-1.4-.642-1.852-1.764-.113-.28-.324-.333-.472-.119a1.064,1.064,0,0,0-.063.9C.651,3.826,1.522,4.651,2.456,4.651A2.588,2.588,0,0,0,4.559,3,.458.458,0,0,0,4.619,2.9C5.152,1.855,5.858,1.278,6.61,1.278c.993,0,1.924,1.052,2.43,2.747.091.307.3.414.459.242A1,1,0,0,0,9.626,3.4C9,1.3,7.845,0,6.61,0Z" transform="translate(5.292 0.64) rotate(90)"/>
          <path class="d" d="M7.25,0A2.985,2.985,0,0,1,9.466,1.128,7.24,7.24,0,0,1,10.88,3.853a1.617,1.617,0,0,1-.272,1.491.865.865,0,0,1-.634.287.972.972,0,0,1-.908-.782C8.657,3.48,7.927,2.56,7.25,2.56c-.49,0-1.006.464-1.418,1.272a1.15,1.15,0,0,1-.093.156A3.159,3.159,0,0,1,3.1,5.933C1.887,5.933.8,4.962.115,3.269a1.7,1.7,0,0,1,.128-1.5.891.891,0,0,1,.733-.41.943.943,0,0,1,.862.652c.34.84.822,1.362,1.258,1.362.372,0,.773-.371,1.1-1.017l0-.01a1.257,1.257,0,0,1,.085-.14A3.549,3.549,0,0,1,7.25,0Z" transform="translate(5.933 0) rotate(90)"/>
        </g>
      </g>
      <path class="c" d="M 17.961 40.531 L 17.961 28.955 L 11 28.955 C 4.925 28.955 0 24.03 0 17.955 L 0 11 C 0 4.925 4.925 0 11 0 L 37.477 0 C 43.552 0 48.477 4.925 48.477 11 L 48.477 17.955 C 48.477 24.03 43.552 28.955 37.477 28.955 L 30.9 28.955 L 30.9 40.531 C 30.9 45.512 25.508 48.624 21.195 46.134 C 19.193 44.978 17.96 42.843 17.96 40.531 L 17.961 40.531 Z M 36.236 8.311 C 36.231 11.159 39.312 12.944 41.781 11.524 C 44.25 10.104 44.256 6.544 41.791 5.116 C 41.227 4.789 40.588 4.617 39.936 4.617 C 37.895 4.617 36.239 6.27 36.236 8.311 Z M 25.769 8.311 C 25.764 11.159 28.845 12.944 31.314 11.524 C 33.783 10.104 33.789 6.544 31.324 5.116 C 30.76 4.789 30.121 4.617 29.469 4.617 C 27.428 4.617 25.772 6.27 25.769 8.311 Z M 15.302 8.311 C 15.297 11.159 18.378 12.944 20.847 11.524 C 23.316 10.104 23.322 6.544 20.857 5.116 C 20.293 4.789 19.654 4.617 19.002 4.617 C 16.96 4.616 15.303 6.269 15.3 8.311 L 15.302 8.311 Z M 4.835 8.311 C 4.83 11.159 7.911 12.944 10.38 11.524 C 12.849 10.104 12.855 6.544 10.39 5.116 C 9.826 4.789 9.187 4.617 8.535 4.617 C 6.494 4.617 4.838 6.27 4.835 8.311 Z" transform="translate(-508 1643)"/>
    </g>
  </g>
</svg>