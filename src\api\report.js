import request from '@/utils/request'

/**
 * 温湿度趋势
 */
export function temphum() {
  return request({
    url: '/saianapi/v1/reports/temphum_trend',
    method: 'GET'
  })
}

/**
 * 温湿度统计
 */
export function temphum_stat() {
  return request({
    url: '/saianapi/v1/reports/temphum_stat',
    mehtod: 'GET'
  })
}

/**
 * 设备在线统计
 */
export function deviceStat() {
  return request({
    url: '/saianapi/v1/reports/device_stat',
    method: 'GET'
  })
}

/**
 * 设备故障统计分析
 */
export function issueStat() {
  return request({
    url: '/saianapi/v1/report/issue_stat',
    method: 'GET'
  })
}

/**
 * 设备总览
 */
export function deviceOverview() {
  return request({
    url: '/saianapi/v1/reports/device_overview',
    method: 'GET'
  })
}

// 统计报告-列表
export function getStatReports(params) {
  return request({
    url: '/saianapi/v5/stat_reports',
    method: 'GET',
    params
  })
}
// 统计报告-详情
export function getStatReportsDetail(id, params) {
  return request({
    url: '/saianapi/v5/stat_reports/' + id,
    method: 'GET',
    params
  })
}
