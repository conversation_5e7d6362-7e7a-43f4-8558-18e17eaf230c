import request from '@/utils/request'
import { getProject, getIdentity } from '@/utils/auth'

// /**
//  * 登录
//  * @param { * } data
//  */
// export function login(data) {
//   return request({
//     url: '/saianapi/v1/web_sessions',
//     method: 'post',
//     data
//   })
// }
/**
 * 第三方登陆
 * @param code值
 */
export function loginByThirdparty(data) {
  return request({
    url: '/saianadmin/v1/wechat_logins',
    method: 'post',
    data
  })
}

/**
 * ott 登陆
 * @param data
 * @returns {AxiosPromise}
 */
export function loginByOtt(data) {
  return request({
    url: 'saianapi/v1/ott_sessions',
    method: 'post',
    data
  })
}

/**
 * 查询用户信息
 */
export function getInfo() {
  return request({
    url: '/saianapi/v1/web_users/' + getIdentity() + '?prjid=' + getProject(),
    method: 'get'
  })
}

/**
 * 查询自己的信息
 */
export function getMyInfo() {
  return request({
    url: '/saianapi/v1/web_users/my?prjid=' + getProject(),
    method: 'get'
  })
}

/**
 * 更新查询自己的信息
 */
export function updateMyInfo(data) {
  return request({
    url: '/saianapi/v1/web_users/my',
    method: 'put',
    data
  })
}

// /**
//  * 退出登录
//  * @param {*} identity
//  */
// export function logout() {
//   return request({
//     url: '/saianapi/v1/web_sessions/' + getIdentity(),
//     method: 'delete'
//   })
// }

/**
 * 添加用户
 * data = { name, account, avatar, mobile, email, password }
 */
export function addUser(query) {
  // const data = Object.assign({}, query, { project_id: getProject() })
  const data = Object.assign({}, query)
  return request({
    url: '/saianapi/v1/web_users',
    method: 'POST',
    data
  })
}

/**
 * 修改用户信息
 * data = { name, avatar, role }
 */
export function updateInfo(identity, data) {
  return request({
    url: '/saianapi/v1/web_users/' + identity,
    method: 'PUT',
    data
  })
}

/**
 * 删除用户
 */
export function deleteUser(identity) {
  return request({
    url: '/saianapi/v1/web_users/' + identity,
    method: 'DELETE'
  })
}

/**
 * 获取用户列表
 */
export function getUsers(query) {
  const params = Object.assign({}, query, { prjid: getProject() })
  return request({
    url: '/saianapi/v1/web_users',
    method: 'GET',
    params
  })
}

/**
 * 找回密码
 * data = { verified_code, new_password }
 * verified_code为手机验证码，new_password为新密码
 */
/* export function changePassword(identity, data) {
  return request({
    url: '/saianapi/v1/web_users/' + identity,
    method: 'PUT',
    data
  })
} */

/**
 * 找回密码
 * @param {mobile,captcha,password} data
 */
export function changePassword(params) {
  return request({
    url: '/saianapi/v1/forgot_passwords',
    method: 'POST',
    params
  })
}

/**
 * 获取手机验证码
 */
// export function getVerifyCode(data) {
//   return request({
//     url: '/saianapi/v1/verified_codes',
//     method: 'POST',
//     data
//   })
// }

/**
 * 根据用户名搜索
 */
// 该接口和'获取用户列表'接口一致,少了个search参数
// export function searchUser(search) {
//   return request({
//     url: '/saianapi/v1/web_users?search=' + search + '&prjid=' + getProject(),
//     method: 'GET'
//   })
// }
export function searchUser(query) {
  const params = Object.assign({}, query, { prjid: getProject() })
  return request({
    url: '/saianapi/v1/web_users',
    method: 'GET',
    params
  })
}

/**
 * 查询角色列表
 */
export function getRoles() {
  return request({
    url: '/saianapi/v1/web_roles',
    method: 'GET'
  })
}

/**
 * 获取登陆验证码
 * @param mobile
 * @returns {*}
 */
export function getLocalVerifiedCode(data) {
  return request({
    url: '/saianapi/v1/local_verified_codes',
    method: 'post',
    data
  })
}

/**
 * 用户登陆
 * @param data
 * @returns {*}
 */
export function webUserLogin(data) {
  return request({
    url: '/saianapi/v1/web_user_sessions',
    method: 'post',
    data
  })
}

/**
 * 用户登出
 * @returns {*}
 */
export function webUserLogout() {
  return request({
    url: '/saianapi/v1/web_user_sessions',
    method: 'delete'
  })
}

/**
 * 修改密码
 * @param data
 * @returns {*}
 */
export function changeLocalPassword(data) {
  return request({
    url: '/saianapi/v1/local_forgot_passwords',
    method: 'post',
    data
  })
}

// 更新用户信息
export function updateWebUserInfo(identify, data) {
  return request({
    url: '/saianapi/v1/web_users/'
  })
}

// 获取账号密码登陆验证码
export function getLoginCaptchaImage(data) {
  return request({
    url: '/saianapi/v1/login_captcha',
    method: 'post',
    data
  })
}

// /**
//  * 查询查询自己的信息
//  * @returns {*}
//  */
// export function getMyUserInfo() {
//   return request({
//     url: '/saianapi/v1/web_users/my',
//     method: 'get'
//   })
// }

export function getUserLogs(params) {
  return request({
    url: 'saianapi/v5/user_logs',
    method: 'get',
    params
  })
}

// 查询用户统计数据
export function getMyStats(params) {
  return request({
    url: 'saianapi/v5/my_stats',
    method: 'get',
    params
  })
}

// 人为创建日志记录
export function createUserLogs(data) {
  return request({
    url: 'saianapi/v5/user_logs',
    method: 'post',
    data
  })
}

/**
 * Web用户登录
 */
export function login(data) {
  return request({
    url: '/saianadmin/v1/web_sessions',
    method: 'post',
    data
  })
}

/**
 * Web用户退出登录
 * @param {*} identity
 */
export function logout() {
  return request({
    url: '/saianadmin/v1/web_sessions/' + getIdentity(),
    method: 'delete'
  })
}

// Web用户绑定微信
export function userBindingWx(data) {
  // code
  return request({
    url: 'saianadmin/v1/web_users/my',
    method: 'put',
    data
  })
}

/**
 * 获取手机验证码
 */
export function getVerifyCode(data) {
  return request({
    url: '/saianadmin/v1/verified_codes',
    method: 'POST',
    data
  })
}
