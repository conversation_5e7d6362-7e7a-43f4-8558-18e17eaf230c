import request from '@/utils/request'
import { getProject } from '@/utils/auth'

/**
 * 获取房间列表（带温湿度）
 * query = { building_id, floor_id, search, page, per_page}
 * 其中有floor_id的时候忽略building_id
 */
export function getRooms(query) {
  query.project_id = getProject()
  return request({
    url: '/saianapi/v1/temphums',
    method: 'GET',
    params: query
  })
}

/**
 * 根据房间查询温湿度趋势
 */
export function getTemphumTrend(query) {
  return request({
    url: '/saianapi/v1/room_hourly_temphums',
    method: 'GET',
    params: query
  })
}

/**
 * 根据房间和日期导出小时温湿度统计
 * query = { room_id, date }
 */
export function getTemphumByRoom(query) {
  return request({
    url: '/saianapi/v1/room_hourly_temphums',
    method: 'GET',
    params: query
  })
}

/**
 * 根据设备和日期导出小时温湿度统计
 * query = { device_id, date }
 */
export function getTemphumByDevice(query) {
  return request({
    url: '/saianapi/v1/device_hourly_temphums',
    method: 'GET',
    params: query
  })
}

/**
 * 根据房间和日期范围查询小时温湿度趋势，如果query为{}，表示查询当天
 * query = { room_id, from, till }
 */
export function getTempTrendByRoom(room_id, query) {
  return request({
    url: '/saianapi/v1/room_hourly_temphums/' + room_id,
    method: 'GET',
    params: query
  })
}

/**
 * 根据房间和日期范围查询日温湿度趋势，如果query为{}，表示查询前一日
 * query = { room_id, from, till }
 */
export function getDailyTrend(room_id, query) {
  return request({
    url: '/saianapi/v1/room_daily_temphums/' + room_id,
    method: 'GET',
    params: query
  })
}

/*
 * 导出房间温湿度图表
 * params = {arid,month}
 */
export function getExcelByRoom(params) {
  return request({
    url: 'saianapi/v1/dl_room_stats',
    method: 'POST',
    params
  })
}
