import request from '@/utils/request'

// 联动规则

/** 规则列表
 * @param {*} search 联动规则名称关键字
 * */
export function getLinkageRules(search) {
  return request({
    url: '/saianapi/v1/linkage_rules',
    method: 'get',
    params: search
  })
}

/** 创建规则
 * @param {*} data
 * */
export function createLinkageRules(data) {
  return request({
    url: '/saianapi/v1/linkage_rules',
    method: 'post',
    data
  })
}

/** 更新规则
 * @param {*} id 联动规则id
 * @param {*} data
 * */
export function updateLinkageRules(id, data) {
  return request({
    url: '/saianapi/v1/linkage_rules/' + id,
    method: 'put',
    data
  })
}

/** 查询规则详情
 * @param {*} id 联动规则id
 * */
export function getLinkageRulesDetail(id) {
  return request({
    url: '/saianapi/v1/linkage_rules/' + id,
    method: 'get'
  })
}

/** 删除规则
 * @param {*} id 联动规则id
 * */
export function deleteLinkageRule(id) {
  return request({
    url: '/saianapi/v1/linkage_rules/' + id,
    method: 'delete'
  })
}

/** 试执行规则
 * @param {*} data 联动id-rule_id,是否试执行-is_trial
 * */
export function executeRule(data) {
  return request({
    url: '/saianapi/v1/execute_rules',
    method: 'post',
    data
  })
}

// 目标设置

/** 目标列表
 * @param {*} rule_id
 * params
 * */
export function getLinkageTargets(params) {
  return request({
    url: '/saianapi/v1/linkage_targets',
    method: 'get',
    params: params
  })
}

/** 新增目标
 * @param {*} data
 * */
export function createLinkageTargets(data) {
  return request({
    url: '/saianapi/v1/linkage_targets',
    method: 'post',
    data
  })
}
/** 修改目标
 * @param {*} data,
 * @param {*}linkaget_target_id 联动目标id
 * */
export function updateLinkageTargets(linkaget_target_id, data) {
  return request({
    url: `/saianapi/v1/linkage_targets/${linkaget_target_id}`,
    method: 'put',
    data
  })
}

/** 删除目标
 * @param {*} linkaget_target_id
 * */
export function deleteLinkageTargets(linkaget_target_id) {
  return request({
    url: `/saianapi/v1/linkage_targets/${linkaget_target_id}`,
    method: 'delete'
  })
}

// 联动变量

/** 变量列表
 * @param {*} rule_id
 * */
export function getLinkageVars(rule_id) {
  return request({
    url: '/saianapi/v1/linkage_vars?rule_id=' + rule_id,
    method: 'get'
  })
}
/** 新增变量
 * @param {*} data
 * */
export function createLinkageVars(data) {
  return request({
    url: '/saianapi/v1/linkage_vars',
    method: 'post',
    data
  })
}
/** 修改变量
 * @param {*} linkage_var_id
 * @param {*} data
 * */
export function updateLinkageVars(linkage_var_id, data) {
  return request({
    url: `/saianapi/v1/linkage_vars/${linkage_var_id}`,
    method: 'put',
    data
  })
}
/** 删除变量
 * @param {*} linkage_var_id
 * */
export function deleteLinkageVars(linkage_var_id) {
  return request({
    url: `/saianapi/v1/linkage_vars/${linkage_var_id}`,
    method: 'delete'
  })
}

