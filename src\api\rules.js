import request from '@/utils/request'

// 获取规则列表
export function getRules(params) {
  return request({
    url: `/saianapi/v1/linkage_rules`,
    method: 'get',
    params
  })
}
// 新增规则
export function addRule(data) {
  return request({
    url: `/saianapi/v1/linkage_rules`,
    method: 'post',
    data
  })
}
// 修改规则
export function updateRule(rule_id, data) {
  return request({
    url: `/saianapi/v1/linkage_rules/${rule_id}`,
    method: 'put',
    data
  })
}
// 规则详情
export function getRuleDetail(rule_id) {
  return request({
    url: `/saianapi/v1/linkage_rules/${rule_id}`,
    method: 'get'
  })
}
// 删除规则
export function deleteRule(rule_id) {
  return request({
    url: `/saianapi/v1/linkage_rules/${rule_id}`,
    method: 'delete'
  })
}
// 试执行规则

export function executeRules(data) {
  return request({
    url: `/saianapi/v1/execute_rules`,
    method: 'post',
    data
  })
}

// 变量列表
export function getVars(params) {
  return request({
    url: `/saianapi/v1/linkage_vars`,
    method: 'get',
    params
  })
}
// 新增变量
export function addVar(data) {
  return request({
    url: `/saianapi/v1/linkage_vars`,
    method: 'post',
    data
  })
}
// 修改变量
export function updateVar(linkage_var_id, data) {
  return request({
    url: `/saianapi/v1/linkage_vars/${linkage_var_id}`,
    method: 'put',
    data
  })
}
// 删除变量
export function deleteVar(linkage_var_id, data) {
  return request({
    url: `/saianapi/v1/linkage_vars/${linkage_var_id}`,
    method: 'delete',
    data
  })
}
