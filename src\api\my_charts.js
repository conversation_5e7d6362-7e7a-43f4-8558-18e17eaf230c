import request from '@/utils/request'
// import { getProject } from '@/utils/auth'

/**
 * 获取图表列表
 */
export function getWebCharts() {
  return request({
    url: '/saianapi/v1/web_charts',
    method: 'GET'
  })
}

/**
 * 新增自定义图表
 */
export function createWebChart(data) {
  return request({
    url: '/saianapi/v1/web_charts',
    method: 'POST',
    data
  })
}

/**
 * 查询自定义图表
 */
export function getWebChartDetail(id) {
  return request({
    url: '/saianapi/v1/web_charts/' + id,
    method: 'get'
  })
}

/**
 * 修改自定义图表
 */
export function updateWebChart(id, data) {
  return request({
    url: '/saianapi/v1/web_charts/' + id,
    method: 'PUT',
    data
  })
}

/**
 * 删除自定义图表
 */
export function deleteWebChart(id) {
  return request({
    url: '/saianapi/v1/web_charts/' + id,
    method: 'DELETE'
  })
}

/**
 * 用户图表-获取列表
 */
export function getUserCharts() {
  return request({
    url: '/saianapi/v1/user_charts',
    method: 'GET'
  })
}

/**
 * 用户图表-新增自定义图表
 */
export function createUserChart(data) {
  return request({
    url: '/saianapi/v1/user_charts',
    method: 'POST',
    data
  })
}

/**
 * 用户图表-查询自定义图表
 */
export function getUserChartDetail(id) {
  return request({
    url: '/saianapi/v1/user_charts/' + id,
    method: 'get'
  })
}

/**
 * 用户图表-修改自定义图表
 */
export function updateUserChart(id, data) {
  return request({
    url: '/saianapi/v1/user_charts/' + id,
    method: 'PUT',
    data
  })
}
/**
 *用户图表- 删除自定义图表
 */
export function deleteUserChart(id) {
  return request({
    url: '/saianapi/v1/user_charts/' + id,
    method: 'DELETE'
  })
}

/**
 * 用户图表组-获取列表
 */
export function getUserAnalyses() {
  return request({
    url: '/saianapi/v1/user_analysis',
    method: 'GET'
  })
}

/**
 * 用户图表组-新增自定义图表组
 */
export function createUserAnalyses(data) {
  return request({
    url: '/saianapi/v1/user_analysis',
    method: 'POST',
    data
  })
}

/**
 * 用户图表组-查询自定义图表组
 */
export function getUserAnalysesDetail(id) {
  return request({
    url: '/saianapi/v1/user_analysis/' + id,
    method: 'get'
  })
}

/**
 * 用户图表组-修改自定义图表组
 */
export function updateUserAnalyses(id, data) {
  return request({
    url: '/saianapi/v1/user_analysis/' + id,
    method: 'PUT',
    data
  })
}

/**
 *用户图表组- 删除自定义图表组
 */
export function deleteUserAnalyses(id) {
  return request({
    url: '/saianapi/v1/user_analysis/' + id,
    method: 'DELETE'
  })
}

/**
 *用户图表组-往图表组添加图表
 */
export function addAnalysesCharts(data) {
  return request({
    url: '/saianapi/v1/analyse_charts',
    method: 'POST',
    data
  })
}
/**
 *用户图表组-修改图表组图表（暂先修改顺序）
 */
export function changeAnalysesCharts(id, data) {
  return request({
    url: '/saianapi/v1/analyse_charts/' + id,
    method: 'PUT',
    data
  })
}
/**
 *用户图表组-从图表组移除图表
 */
export function deleteAnalysesCharts(id, data) {
  return request({
    url: '/saianapi/v1/analyse_charts/' + id,
    method: 'delete',
    data
  })
}

/**
 *手工数据-查询数据列表
 */
export function getManualRecords(params) {
  return request({
    url: '/saianapi/v1/manual_records',
    method: 'GET',
    params
  })
}

/**
 *手工数据-录入数据
 */
export function addManualRecords(data) {
  return request({
    url: '/saianapi/v1/manual_records',
    method: 'POST',
    data
  })
}

/**
 *手工数据-修改录入数据
 */
export function updateManualRecords(id, data) {
  return request({
    url: '/saianapi/v1/manual_records/' + id,
    method: 'PUT',
    data
  })
}

/**
 *手工数据-删除录入数据
 */
export function deleteManualRecords(id) {
  return request({
    url: '/saianapi/v1/manual_records/' + id,
    method: 'DELETE'
  })
}

/**
 *查询图表数据
 */
export function getChartData(params) {
  return request({
    url: '/saianapi/v5/chart_data',
    method: 'GET',
    params
  })
}

/**
 *通用数据导出
 */
export function getGenerateExport(data) {
  return request({
    url: '/saianapi/v5/derive_data',
    method: 'post',
    data
  })
}

/**
 *通用数据导出时下载文件
 */
export function downLoadFile(filename) {
  return request({
    url: `/drf-assets/files/${encodeURIComponent(filename)}.xlsx`,
    responseType: 'blob',
    responseEncoding: 'utf8',
    method: 'get'
  })
}
