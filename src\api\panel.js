import request from '@/utils/request'

/**
 * 查询项目能耗情况
 */
export function getPanelSummary() {
  return request({
    url: '/saianapi/v1/panel_summary',
    method: 'get'
  })
}

/**
 * 查询项目气温
 */
export function getPanelWeather(type) {
  return request({
    url: '/saianapi/v1/panel_weather?type=' + type,
    method: 'GET'
  })
}

/**
 * 查询空调电耗
 */
export function getPanelEcPower(type) {
  const params = { type }

  return request({
    url: '/saianapi/v1/ec_power',
    method: 'get',
    params
  })
}

/**
 * 查询空调冷耗
 * @param type
 */
export function getPanelEcCold(type) {
  const params = { type }
  return request({
    url: '/saianapi/v1/ec_cold',
    method: 'get',
    params
  })
}

/**
 * 查询空调节能率
 * @param type
 */
export function getPanelEsrPower(type) {
  const params = { type }

  return request({
    url: '/saianapi/v1/esr_power',
    method: 'get',
    params
  })
}

/**
 * 查询空调节冷率
 * @param type
 */
export function getPanelEsrCold(type) {
  const params = { type }
  return request({
    url: '/saianapi/v1/esr_cold',
    method: 'get',
    params
  })
}

/**
 * 查询设备离线率
 */
export function getDeviceOfflineTop() {
  return request({
    url: '/saianapi/v1/offline_rate',
    method: 'get'
  })
}

/**
 * 设备报警简单统计
 */
export function getAlarmSummary() {
  return request({
    url: '/saianapi/v1/panel_alarm_summary',
    method: 'get'
  })
}

/**
 * 查询设备故障原因排行
 */
export function getFaultReasonStats() {
  return request({
    url: '/saianapi/v1/fault_reason_stats',
    method: 'get'
  })
}

/**
 * 设备30天故障率
 */
export function getFaultRate30d() {
  return request({
    url: '/saianapi/v1/fault_rate_30d',
    method: 'get'
  })
}

/**
 * 用户关注度排行
 */
export function getDeviceFollower() {
  return request({
    url: '/saianapi/v1/device_follower',
    method: 'get'
  })
}

/**
 * 设备关注度排行
 */
export function getDeviceFollowing() {
  return request({
    url: '/saianapi/v1/device_following',
    method: 'get'
  })
}

/**
 * 获取用户面板所有的模块
 */
export function getWebPanels() {
  return request({
    url: '/saianapi/v1/web_panels',
    method: 'get'
  })
}

/**
 * 获取项目已配置模块
 */
export function getProjectPanels() {
  return request({
    url: 'saianapi/v1/project_panels',
    method: 'get'
  })
}

/**
 * 配置用户看板功能模块位置
 * @param web_panel_id
 * @param num
 */
export function configProjectPanel(web_panel_id, num) {
  const data = {
    web_panel_id,
    num
  }

  return request({
    url: '/saianapi/v1/project_panels',
    method: 'post',
    data
  })
}

/**
 * 删除用户看板功能模块
 * @param web_panel_id
 */
export function deleteProjectPanel(web_panel_id) {
  return request({
    url: '/saianapi/v1/project_panels/' + web_panel_id,
    method: 'delete'
  })
}

/**
 * 获取实时天气
 */
export function getWeather() {
  return request({
    url: '/saianapi/v1/weather',
    method: 'get'
  })
}

/**
 * 获取用户面板设置
 */
export function getPanelSettings() {
  return request({
    url: '/saianapi/v1/panel_settings',
    method: 'get'
  })
}

/**
 * 保存用户面板设置
 * @param data
 */
export function savePanelSettings(data) {
  return request({
    url: '/saianapi/v1/panel_settings',
    method: 'put',
    data
  })
}
