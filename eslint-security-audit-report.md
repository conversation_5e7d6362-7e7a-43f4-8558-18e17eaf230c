# ESLint 代码安全检查报告

## ESLint 运行状态

### 解析错误

```
token.type.endsWith is not a function
Occurred while linting W:\赛安物联\ac-web-standard\src\views\floor\components\ToolBar.vue:395
```

**错误分析**: ESLint解析器版本兼容性问题，建议更新相关依赖包。

## 🔴 严重安全风险

### 1. 代码注入漏洞 - eval() 使用

**风险等级**: 严重 (Critical)  
**CWE**: CWE-94 (代码注入)  
**OWASP**: A03:2021 – Injection

#### 发现位置

- `src/views/svg/utils.js:37`
- `src/components/PlanTools/EditorTool.vue:614`
- `src/components/PlanTools/widgets/ConditionImage.vue:46`
- `src/components/PlanTools/widgets/DeviceParam.vue:91`
- `src/components/PlanTools/widgets/LinkJump.vue:90`

#### 问题代码

```javascript
// src/views/svg/utils.js:30-46
export function evalCondition(condition, value) {
  if (isCallFunc(condition)) {
    return false
  }
  let result = false
  try {
    // eslint-disable-next-line no-eval
    result = eval(condition)  // 🚨 严重安全风险
    if (typeof result !== 'boolean') {
      return false
    }
  } catch (e) {
    return false
  }
  return result
}
```

```javascript
// src/components/PlanTools/EditorTool.vue:610-623
try {
  // eslint-disable-next-line no-unused-vars
  const value = '100'
  // eslint-disable-next-line no-eval
  result = eval(cover.condition)  // 🚨 严重安全风险
} catch (e) {
  if (e.name === 'ReferenceError') {
    this.$message.error(`引用错误，变量 ${e.message.split(' ')[0]} 不存在`)
  } else {
    this.$message.error('条件字符串不正确')
  }
  this.$store.dispatch('cover/setActive', { id: index })
  throw new Error('EvalError')
}
```

```javascript
// src/components/PlanTools/widgets/ConditionImage.vue:35-55
evalCondition(condition, value) {
  if (isCallFunc(condition)) {
    return false
  }
  let result = false
  try {
    // eslint-disable-next-line no-eval
    result = eval(condition)  // 🚨 严重安全风险
    if (typeof result !== 'boolean') {
      return false
    }
  } catch (e) {
    return false
  }
  return result
}
```

**安全影响**:

- 任意代码执行
- 系统权限提升
- 数据泄露
- 恶意脚本注入

**修复建议**:

```javascript
// 安全替代方案 1: 使用 Function 构造函数
function safeEval(expression) {
  try {
    const func = new Function('value', `return ${expression}`)
    return func(value)
  } catch (e) {
    return false
  }
}

// 安全替代方案 2: 使用表达式解析库
import { evaluate } from 'mathjs'
function safeEvaluate(expression, scope) {
  try {
    return evaluate(expression, scope)
  } catch (e) {
    return false
  }
}
```

### 2. XSS 攻击风险 - 危险的HTML渲染

**风险等级**: 严重 (Critical)  
**CWE**: CWE-79 (跨站脚本)  
**OWASP**: A03:2021 – Injection

#### 发现位置

- `src/utils/request.js:281`

#### 问题代码

```javascript
// src/utils/request.js:275-285
Message({
  message: message,
  dangerouslyUseHTMLString: true,  // 🚨 XSS风险
  type: 'error',
  duration: 5 * 1000
})
```

**安全影响**:

- 跨站脚本攻击(XSS)
- 会话劫持
- 恶意重定向
- 敏感信息窃取

**修复建议**:

```javascript
// 安全修复
Message({
  message: this.escapeHtml(message), // 转义HTML
  type: 'error',
  duration: 5 * 1000
  // 移除 dangerouslyUseHTMLString: true
})

// HTML转义函数
escapeHtml(text) {
  const div = document.createElement('div')
  div.textContent = text
  return div.innerHTML
}
```

### 3. DOM操作安全风险

**风险等级**: 严重 (Critical)  
**CWE**: CWE-79 (跨站脚本)

#### 发现位置

- `src/utils/index.js:181-185`
- `src/views/floor/index.vue:1249-1278`

#### 问题代码

```javascript
// src/utils/index.js:181-185
export function html2Text(val) {
  const div = document.createElement('div')
  div.innerHTML = val  // 🚨 潜在XSS风险
  return div.textContent || div.innerText
}
```

```javascript
// src/views/floor/index.vue:1249-1278
mouseOver(e, ele) {
  if (e.currentTarget.shape === 'rect') {
    var mes = document.getElementById('mes')
    var x = e.layerX
    var y = e.layerY
    mes.style.left = x + 'px'  // 🚨 直接DOM操作
    mes.style.top = y + 20 + 'px'
    mes.style.display = 'block'
    mes.style.zIndex = 9999
    // ... 更多直接DOM操作
  }
}
```

**修复建议**:

```javascript
// 安全的HTML转文本
export function html2Text(val) {
  const parser = new DOMParser()
  const doc = parser.parseFromString(val, 'text/html')
  return doc.body.textContent || doc.body.innerText || ''
}
```

## 🟡 中等安全风险

### 4. 不安全的Function构造函数使用

**风险等级**: 中等 (Medium)  
**CWE**: CWE-94 (代码注入)

#### 发现位置

- `src/views/my-charts/index.vue:2886-2889`

#### 问题代码

```javascript
// src/views/my-charts/index.vue:2886-2889
evil(fn) {
  const Fn = Function
  return new Fn('return ' + fn)()  // 🚨 代码注入风险
}
```

**修复建议**: 避免动态代码执行，使用安全的替代方案

### 5. URL参数解析安全风险

**风险等级**: 中等 (Medium)  
**CWE**: CWE-20 (输入验证不当)

#### 发现位置

- `src/utils/index.js:161-175`

#### 问题代码

```javascript
// src/utils/index.js:161-175
export function param2Obj(url) {
  const search = url.split('?')[1]
  if (!search) {
    return {}
  }
  return JSON.parse(  // 🚨 JSON解析风险
    '{"' +
      decodeURIComponent(search)  // 🚨 未验证的URL解码
        .replace(/"/g, '\\"')
        .replace(/&/g, '","')
        .replace(/=/g, '":"')
        .replace(/\+/g, ' ') +
      '"}'
  )
}
```

**安全影响**:

- JSON注入攻击
- 原型污染
- 拒绝服务攻击

**修复建议**:

```javascript
export function param2Obj(url) {
  const search = url.split('?')[1]
  if (!search) {
    return {}
  }
  
  const params = {}
  const pairs = search.split('&')
  
  for (const pair of pairs) {
    const [key, value] = pair.split('=')
    if (key && value) {
      try {
        params[decodeURIComponent(key)] = decodeURIComponent(value)
      } catch (e) {
        // 忽略无效的URL编码
        continue
      }
    }
  }
  
  return params
}
```

### 6. 文件下载安全风险

**风险等级**: 中等 (Medium)  
**CWE**: CWE-22 (路径遍历)

#### 发现位置

- `src/views/my-charts/index.vue:2862-2881`

#### 问题代码

```javascript
// src/views/my-charts/index.vue:2862-2881
saveAs(blob, filename) {
  const blob2 = new Blob([blob], { type: 'application/vnd.ms-excel' })
  if (window.navigator.msSaveOrOpenBlob) {
    navigator.msSaveBlob(blob, filename)  // 🚨 文件名未验证
  } else {
    var link = document.createElement('a')
    var body = document.querySelector('body')
    link.href = window.URL.createObjectURL(blob2)
    link.download = filename  // 🚨 文件名未验证
    
    // fix Firefox
    link.style.display = 'none'
    body.appendChild(link)
    link.click()
    body.removeChild(link)
    window.URL.revokeObjectURL(link.href)
  }
}
```

**修复建议**:

```javascript
// 验证文件名
function sanitizeFilename(filename) {
  return filename.replace(/[^a-zA-Z0-9.-]/g, '_')
}

saveAs(blob, filename) {
  const safeFilename = sanitizeFilename(filename)
  // ... 其余代码
}
```

## 🟢 低等安全风险

### 7. 控制台信息泄露

**风险等级**: 低等 (Low)  
**CWE**: CWE-532 (日志文件中的敏感信息)

#### 发现位置: 多个文件

#### 问题代码

```javascript
// src/views/svg/utils.js:54
console.log('requireComponent', requireComponent.keys())

// src/views/svg/utils.js:65  
console.log('components/', components)

// src/views/floor/components/ToolBar.vue:806
console.log('change rotate: ', e.target.value)

// src/components/PlanTools/EditorTool.vue:639
console.log(e)
```

**修复建议**: 生产环境移除所有console语句

### 8. 正则表达式安全

**风险等级**: 低等 (Low)  
**CWE**: CWE-1333 (ReDoS)

#### 发现位置

- `src/views/svg/utils.js:22`

#### 问题代码

```javascript
// src/views/svg/utils.js:21-27
export function isCallFunc(str) {
  const reg = /\w+\(.*\)/  // 🚨 潜在ReDoS风险
  const matchResults = str.match(reg)
  if (matchResults && matchResults.length && matchResults[0].startsWith('indexOf(')) {
    return false
  }
  return reg.test(str)
}
```

## 修复优先级

### 🔴 立即修复 (严重)

1. **移除所有eval()使用** - 使用安全的表达式解析器
2. **修复XSS风险** - 移除dangerouslyUseHTMLString
3. **安全化DOM操作** - 验证和转义所有用户输入

### 🟡 短期修复 (中等)  

1. **替换Function构造函数** - 使用安全的替代方案
2. **改进URL解析** - 添加输入验证
3. **文件名验证** - 防止路径遍历攻击

### 🟢 长期改进 (低等)

1. **移除调试日志** - 生产环境清理
2. **优化正则表达式** - 防止ReDoS攻击
3. **建立安全开发流程** - 定期安全审计

## ESLint 安全规则建议

```javascript
// .eslintrc.js 安全配置
module.exports = {
  rules: {
    'no-eval': 'error',                    // 禁用eval
    'no-implied-eval': 'error',            // 禁用隐式eval
    'no-new-func': 'error',                // 禁用Function构造函数
    'no-script-url': 'error',              // 禁用javascript:url
    'no-unsafe-innerhtml/no-unsafe-innerhtml': 'error'
  },
  plugins: [
    'no-unsafe-innerhtml',
    'security'
  ]
}
```

## 总结

**总体安全评级**: D级 (高风险)

**关键发现**:

- 5个严重安全漏洞 (eval使用、XSS风险)
- 3个中等安全风险 (Function构造函数、URL解析)
- 2个低等安全风险 (信息泄露、ReDoS)

**建议**: 立即修复所有严重安全问题，特别是eval()使用和XSS风险，这些可能导致严重的安全事故。
