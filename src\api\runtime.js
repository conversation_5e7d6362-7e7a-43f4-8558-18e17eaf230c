import request from '@/utils/request'

// 设备运行时间

// 获取设备列表
export function getDeviceList(params) {
  return request({
    url: '/saianapi/v2/web_devices',
    method: 'get',
    params
  })
}
/** 列表
 * */
export function getDeviceRunTime(params) {
  return request({
    url: '/saianapi/v5/device_runtime',
    method: 'get',
    params
  })
}

export function getAttrProto(params) {
  // params.read_only = 0
  return request({
    url: `/saianapi/v1/attribute_prototypes`,
    method: 'get',
    params
  })
}
