<?xml version="1.0" encoding="utf-8"?>
<svg width="56" height="56" viewBox="0 0 56 56" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <style>.a{fill:url(#a);}.b{clip-path:url(#b);}.c{fill:url(#c);}.d{fill:url(#d);}</style>
    <linearGradient id="a" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#f7d15e"/>
      <stop offset="1" stop-color="#f3a64f"/>
    </linearGradient>
    <clipPath id="b">
      <rect class="a" width="56" height="56"/>
    </clipPath>
    <linearGradient id="c" y1="1.357" x2="0.552" y2="-1.08" xlink:href="#a"/>
    <linearGradient id="d" x1="-1.184" y1="-6.31" x2="3.879" y2="-6.518" xlink:href="#a"/>
  </defs>
  <g class="b">
    <g transform="translate(4 1)">
      <path class="c" d="M 21.239 11.001 C 19.065 11 17.376 9.106 17.623 6.946 L 13.978 6.946 C 10.92 6.897 9.061 3.557 10.632 0.933 C 11.338 -0.247 12.604 -0.977 13.978 -0.999 L 34.506 -0.999 C 37.564 -0.95 39.423 2.39 37.852 5.014 C 37.146 6.194 35.88 6.924 34.506 6.946 L 31.336 6.946 C 31.583 9.105 29.897 10.998 27.724 11.001 L 21.239 11.001 Z" style="transform-box: fill-box; transform-origin: 50% 50%;" transform="matrix(-1, 0, 0, -1, -0.000004, 0.000001)"/>
      <g transform="translate(3.855 36.176)">
        <g transform="translate(28.424 0)">
          <path class="d" d="M6.61,4.651c-.91,0-1.767-.678-2.42-1.913a.519.519,0,0,1-.061-.094c-.448-.882-1.041-1.368-1.673-1.368-.724,0-1.4.643-1.852,1.764-.113.28-.324.333-.472.118a1.064,1.064,0,0,1-.063-.9C.651.825,1.522,0,2.456,0a2.588,2.588,0,0,1,2.1,1.653.452.452,0,0,1,.061.095C5.152,2.8,5.858,3.373,6.61,3.373c.993,0,1.924-1.052,2.43-2.747.091-.307.3-.414.459-.242a1,1,0,0,1,.127.871C9,3.35,7.845,4.651,6.61,4.651Z" transform="translate(5.292 0.64) rotate(90)"/>
          <path class="d" d="M7.25,5.933A2.985,2.985,0,0,0,9.466,4.805,7.24,7.24,0,0,0,10.88,2.08,1.617,1.617,0,0,0,10.608.589.865.865,0,0,0,9.974.3a.972.972,0,0,0-.908.782C8.657,2.453,7.927,3.373,7.25,3.373c-.49,0-1.006-.464-1.418-1.272a1.144,1.144,0,0,0-.094-.156A3.159,3.159,0,0,0,3.1,0C1.887,0,.8.971.115,2.664a1.7,1.7,0,0,0,.128,1.5.891.891,0,0,0,.733.41.943.943,0,0,0,.862-.652c.34-.84.822-1.362,1.258-1.362.372,0,.773.371,1.1,1.017a1.232,1.232,0,0,0,.09.149A3.549,3.549,0,0,0,7.25,5.933Z" transform="translate(5.933 0) rotate(90)"/>
          <path class="d" d="M6.61,4.651c-.91,0-1.767-.678-2.42-1.913a.552.552,0,0,1-.061-.094c-.448-.882-1.041-1.368-1.673-1.368-.724,0-1.4.642-1.852,1.764-.113.28-.324.333-.472.119a1.064,1.064,0,0,1-.063-.9C.651.825,1.522,0,2.456,0a2.588,2.588,0,0,1,2.1,1.653.458.458,0,0,1,.061.095C5.152,2.8,5.858,3.373,6.61,3.373c.993,0,1.924-1.052,2.43-2.747.091-.307.3-.414.459-.242a1,1,0,0,1,.127.871C9,3.35,7.845,4.651,6.61,4.651Z" transform="translate(11.702 0.64) rotate(90)"/>
          <path class="d" d="M7.25,5.933A2.985,2.985,0,0,0,9.466,4.805,7.24,7.24,0,0,0,10.88,2.08,1.617,1.617,0,0,0,10.608.589.865.865,0,0,0,9.974.3a.972.972,0,0,0-.908.782C8.657,2.453,7.927,3.373,7.25,3.373c-.49,0-1.006-.464-1.418-1.272a1.15,1.15,0,0,0-.093-.156A3.159,3.159,0,0,0,3.1,0C1.887,0,.8.971.115,2.664a1.7,1.7,0,0,0,.128,1.5.891.891,0,0,0,.733.41.943.943,0,0,0,.862-.652c.34-.84.822-1.362,1.258-1.362.372,0,.773.371,1.1,1.017l0,.01a1.257,1.257,0,0,0,.085.14A3.549,3.549,0,0,0,7.25,5.933Z" transform="translate(12.343 0) rotate(90)"/>
        </g>
        <g transform="translate(0 0)">
          <path class="d" d="M6.61,4.651c-.91,0-1.767-.678-2.42-1.913a.519.519,0,0,1-.061-.094c-.448-.882-1.041-1.368-1.673-1.368-.724,0-1.4.643-1.852,1.764-.113.28-.324.333-.472.118a1.064,1.064,0,0,1-.063-.9C.651.825,1.522,0,2.456,0a2.588,2.588,0,0,1,2.1,1.653.452.452,0,0,1,.061.095C5.152,2.8,5.858,3.373,6.61,3.373c.993,0,1.924-1.052,2.43-2.747.091-.307.3-.414.459-.242a1,1,0,0,1,.127.871C9,3.35,7.845,4.651,6.61,4.651Z" transform="translate(5.292 0.64) rotate(90)"/>
          <path class="d" d="M7.25,5.933A2.985,2.985,0,0,0,9.466,4.805,7.24,7.24,0,0,0,10.88,2.08,1.617,1.617,0,0,0,10.608.589.865.865,0,0,0,9.974.3a.972.972,0,0,0-.908.782C8.657,2.453,7.927,3.373,7.25,3.373c-.49,0-1.006-.464-1.418-1.272a1.144,1.144,0,0,0-.094-.156A3.159,3.159,0,0,0,3.1,0C1.887,0,.8.971.115,2.664a1.7,1.7,0,0,0,.128,1.5.891.891,0,0,0,.733.41.943.943,0,0,0,.862-.652c.34-.84.822-1.362,1.258-1.362.372,0,.773.371,1.1,1.017a1.232,1.232,0,0,0,.09.149A3.549,3.549,0,0,0,7.25,5.933Z" transform="translate(5.933 0) rotate(90)"/>
          <path class="d" d="M6.61,4.651c-.91,0-1.767-.678-2.42-1.913a.552.552,0,0,1-.061-.094c-.448-.882-1.041-1.368-1.673-1.368-.724,0-1.4.642-1.852,1.764-.113.28-.324.333-.472.119a1.064,1.064,0,0,1-.063-.9C.651.825,1.522,0,2.456,0a2.588,2.588,0,0,1,2.1,1.653.458.458,0,0,1,.061.095C5.152,2.8,5.858,3.373,6.61,3.373c.993,0,1.924-1.052,2.43-2.747.091-.307.3-.414.459-.242a1,1,0,0,1,.127.871C9,3.35,7.845,4.651,6.61,4.651Z" transform="translate(11.702 0.64) rotate(90)"/>
          <path class="d" d="M7.25,5.933A2.985,2.985,0,0,0,9.466,4.805,7.24,7.24,0,0,0,10.88,2.08,1.617,1.617,0,0,0,10.608.589.865.865,0,0,0,9.974.3a.972.972,0,0,0-.908.782C8.657,2.453,7.927,3.373,7.25,3.373c-.49,0-1.006-.464-1.418-1.272a1.15,1.15,0,0,0-.093-.156A3.159,3.159,0,0,0,3.1,0C1.887,0,.8.971.115,2.664a1.7,1.7,0,0,0,.128,1.5.891.891,0,0,0,.733.41.943.943,0,0,0,.862-.652c.34-.84.822-1.362,1.258-1.362.372,0,.773.371,1.1,1.017l0,.01a1.257,1.257,0,0,0,.085.14A3.549,3.549,0,0,0,7.25,5.933Z" transform="translate(12.343 0) rotate(90)"/>
        </g>
      </g>
      <path class="c" d="M 11 47 C 4.925 47 0 42.075 0 36 L 0 29.045 C 0 22.97 4.925 18.045 11 18.045 L 17.961 18.045 L 17.961 6.47 C 17.961 1.489 23.353 -1.623 27.666 0.867 C 29.668 2.023 30.901 4.158 30.901 6.47 L 30.901 18.045 L 37.476 18.045 C 43.551 18.045 48.476 22.97 48.476 29.045 L 48.476 36 C 48.476 42.075 43.551 47 37.476 47 L 11 47 Z M 36.236 38.689 C 36.231 41.537 39.312 43.322 41.781 41.902 C 44.25 40.482 44.256 36.922 41.791 35.494 C 41.227 35.167 40.588 34.995 39.936 34.995 C 37.895 34.995 36.239 36.648 36.236 38.689 Z M 25.769 38.689 C 25.764 41.537 28.845 43.322 31.314 41.902 C 33.783 40.482 33.789 36.922 31.324 35.494 C 30.76 35.167 30.121 34.995 29.469 34.995 C 27.428 34.995 25.772 36.648 25.769 38.689 Z M 15.302 38.689 C 15.297 41.537 18.378 43.322 20.847 41.902 C 23.316 40.482 23.322 36.922 20.857 35.494 C 20.293 35.167 19.654 34.995 19.002 34.995 C 16.96 34.994 15.303 36.647 15.3 38.689 L 15.302 38.689 Z M 4.835 38.689 C 4.83 41.537 7.911 43.322 10.38 41.902 C 12.849 40.482 12.855 36.922 10.39 35.494 C 9.826 35.167 9.187 34.995 8.535 34.995 C 6.494 34.995 4.838 36.648 4.835 38.689 Z" transform="translate(48.478 54) rotate(180)"/>
    </g>
  </g>
</svg>