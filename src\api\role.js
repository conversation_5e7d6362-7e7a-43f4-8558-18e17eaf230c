import request from '@/utils/request'

/**
 * 角色列表
 */
export function getRoles(params) {
  return request({
    url: '/saianapi/v1/web_roles',
    method: 'get',
    params
  })
}

/**
 * 新增角色
 * @param {Object} data
 */
export function addRole(data) {
  return request({
    url: '/saianapi/v1/web_roles',
    method: 'post',
    data
  })
}

/**
 * 角色详情
 * @param {number} id
 */
export function getRoleDetail(id) {
  return request({
    url: `/saianapi/v1/web_roles/${id}`,
    method: 'get'
  })
}

/**
 * 修改角色
 * @param {number} id
 * @param {Object} data
 */
export function changeRole(id, data) {
  return request({
    url: `/saianapi/v1/web_roles/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除角色
 * @param {number} id
 */
export function deleteRole(id) {
  return request({
    url: `/saianapi/v1/web_roles/${id}`,
    method: 'delete'
  })
}
